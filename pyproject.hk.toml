[project]
name = "mcp-cms"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.11.16",
    "aiomysql>=0.2.0",
    "anthropic>=0.49.0",
    "beautifulsoup4>=4.13.4",
    "fastapi-cache>=0.1.0",
    "fastapi-mcp>=0.2.0",
    "firebirdsql>=1.3.5",
    "google-genai>=1.10.0",
    "imapclient>=3.0.1",
    "openai>=1.72.0",
    "openpyxl>=3.1.5",
    "pandas>=2.3.0",
    "paramiko>=3.5.0",
    "passlib>=1.7.4",
    "pypinyin>=0.53.0",
    "pdfkit>=1.0.0",
    "playwright>=1.52.0",
    "prometheus-client>=0.21.1",
    "prometheus-fastapi-instrumentator>=7.1.0",
    "pymysql>=1.1.1",
    "python-dateutil>=2.8.2",
    "python-dotenv>=1.1.0",
    "redis>=5.2.1",
    "reportlab>=4.4.1",
    "sshtunnel>=0.4.0",
    "uvloop>=0.21.0",
    "weasyprint>=65.1",
    "oss2>=2.19.1",
    "pandasai[connectors]>=2.0.24",
    "pyyaml>=6.0.2",
    "sqlalchemy>=1.4.54",
    "python-docx>=1.2.0",
]

# HK阿里云服务器专用配置 - 使用阿里云PyPI镜像
[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true
