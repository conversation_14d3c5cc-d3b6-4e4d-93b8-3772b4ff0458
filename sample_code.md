# 物流业务数据分析 SQL 查询示例代码

## 📋 使用说明

### 统一参数规范
- **占位符**: `:start_date`、`:end_date`、`:company_ids`（数组）、`:top_n`、`:churn_gap_months` 等
- **去重机制**: 所有查询都内置去重 CTE
- **业务模式**: `service_mode` 口径：1=LCL，2=FCL，3=Buy-Consol，4=AIR
- **票数统计**: 海运出口用 `bk_count`，其他用 `bill_count`；TEU 仅 `service_mode=2` 统计
- **转运维度**: 仅香港(852)启用，其它分公司默认忽略（可通过参数开关）

---

## 0️⃣ 公共去重片段（所有查询引用）

**用途**: 标准去重逻辑，确保数据唯一性

```sql
WITH booking_latest AS (
  SELECT *
  FROM (
    SELECT b.*,
           ROW_NUMBER() OVER(
             PARTITION BY b.job_id, b.bkbl_id, b.pro2_system_id
             ORDER BY b.id DESC
           ) AS rn
    FROM t_booking_details b
  ) t
  WHERE rn = 1
),
job_latest AS (
  SELECT *
  FROM (
    SELECT j.*,
           ROW_NUMBER() OVER(
             PARTITION BY j.job_id, j.pro2_system_id
             ORDER BY j.id DESC
           ) AS rn
    FROM t_job_details j
  ) t
  WHERE rn = 1
)
```

**技术说明**: 使用 `ROW_NUMBER()` 做"同键取最新"是标准做法，确保数据唯一性。

---

## 1️⃣ LCL：Coload vs Consol 月度对比

**业务场景**: 分析LCL业务中拼箱(Coload)与整合(Consol)的月度表现对比

```sql
WITH ... -- 引入上面的 booking_latest / job_latest
, lcl_jobs AS (
  SELECT DISTINCT b.job_id, b.pro2_system_id
  FROM booking_latest b
  WHERE b.service_mode = 1
    AND b.job_date BETWEEN :start_date AND :end_date
    AND b.pro2_system_id IN (:company_ids)
)
SELECT
  DATE_FORMAT(j.job_date, '%Y-%m')        AS month,
  CONCAT(j.pol_code,'-',j.pod_code)       AS route,
  CASE WHEN j.is_consolidation=1 THEN 'Consol' ELSE 'Coload' END AS lcl_mode,
  SUM(COALESCE(j.bk_count,0))             AS tickets,
  ROUND(SUM(COALESCE(j.rt,0)),2)          AS rt,
  NULL                                    AS teu,            -- LCL 不统计
  ROUND(SUM(COALESCE(j.income,0)),2)      AS income,
  ROUND(SUM(COALESCE(j.cost,0)),2)        AS cost,
  ROUND(SUM(COALESCE(j.profit,0)),2)      AS profit,
  ROUND(100.0*SUM(COALESCE(j.profit,0))/
        NULLIF(SUM(COALESCE(j.income,0)),0),1) AS margin_pct,
  ROUND(SUM(COALESCE(j.transhipment_profit,0)),2) AS transhipment_profit
FROM job_latest j
JOIN lcl_jobs lj
  ON j.job_id=lj.job_id AND j.pro2_system_id=lj.pro2_system_id
GROUP BY month, route, lcl_mode
ORDER BY month DESC, profit DESC;
```

---

## 2️⃣ 业务大类月度盘点（含转运开关）

**业务场景**: 按业务模式统计月度业绩，支持转运业务开关控制

```sql
WITH ...
SELECT
  DATE_FORMAT(b.job_date, '%Y-%m') AS month,
  CASE
    WHEN b.service_mode=1 AND j.is_consolidation=1 THEN 'LCL-Consol'
    WHEN b.service_mode=1 AND j.is_consolidation=0 THEN 'LCL-Coload'
    WHEN b.service_mode=2 THEN 'FCL'
    WHEN b.service_mode=3 THEN 'Buy-Consol'
    WHEN b.service_mode=4 THEN 'AIR'
    ELSE 'Other'
  END AS biz_mode,
  CASE
    WHEN :enable_transhipment=1 AND b.pro2_system_id=852 THEN b.is_transhipment
    ELSE 0
  END AS is_transhipment,
  COUNT(*) AS bookings,
  ROUND(SUM(COALESCE(b.lcl_rt,0)),2) AS rt,
  ROUND(SUM(CASE WHEN b.service_mode=2 THEN COALESCE(b.teu,0) ELSE 0 END),2) AS teu,
  ROUND(SUM(COALESCE(b.profit,0)),2) AS profit,
  ROUND(SUM(COALESCE(b.transhipment_profit,0)),2) AS transhipment_profit,
  ROUND(SUM(COALESCE(b.total_business_profit,0)),2) AS total_profit
FROM booking_latest b
LEFT JOIN job_latest j
  ON b.job_id=j.job_id AND b.pro2_system_id=j.pro2_system_id
WHERE b.job_date BETWEEN :start_date AND :end_date
  AND b.pro2_system_id IN (:company_ids)
GROUP BY month, biz_mode, is_transhipment
ORDER BY month DESC, total_profit DESC;
```

---

## 3️⃣ 客户新增/流失/复活分析

**业务场景**: 客户生命周期分析，可调窗口与判定标准

```sql
WITH ...
, base AS (
  SELECT client_name,
         DATE_FORMAT(job_date, '%Y-%m') AS ym,
         COUNT(*) AS bookings,
         SUM(COALESCE(profit,0)) AS profit
  FROM booking_latest
  WHERE job_date BETWEEN :start_date AND :end_date
    AND pro2_system_id IN (:company_ids)
  GROUP BY client_name, DATE_FORMAT(job_date, '%Y-%m')
),
last_seen AS (
  SELECT client_name,
         MAX(ym) AS last_ym
  FROM base
  GROUP BY client_name
),
status AS (
  SELECT b.client_name,
         MIN(b.ym) OVER (PARTITION BY b.client_name) AS first_ym_in_window,
         MAX(b.ym) OVER (PARTITION BY b.client_name) AS last_ym_in_window,
         l.last_ym
  FROM base b
  JOIN last_seen l ON b.client_name=l.client_name
)
SELECT
  s.client_name,
  s.first_ym_in_window,
  s.last_ym_in_window,
  CASE
    WHEN TIMESTAMPDIFF(MONTH, STR_TO_DATE(s.last_ym_in_window,'%Y-%m'),
                       STR_TO_DATE(:end_ym,'%Y-%m')) >= :churn_gap_months
      THEN '流失'
    WHEN s.first_ym_in_window = :current_ym
      AND EXISTS (SELECT 1 FROM base b2
                  WHERE b2.client_name=s.client_name
                    AND b2.ym < :current_ym)
      THEN '复活'
    WHEN s.first_ym_in_window = :current_ym THEN '新增'
    ELSE '活跃'
  END AS lifecycle_status
FROM status s
GROUP BY s.client_name, s.first_ym_in_window, s.last_ym_in_window
ORDER BY lifecycle_status, s.client_name;
```

**技术说明**: 用月粒度便于与业务汇报对齐；阈值（如"流失=连续n个月无业务"）用参数控制。

---

## 4️⃣ 客户 Top-N 分析（含 FreeHand vs 指定）

**业务场景**: 分析客户贡献度，区分自揽与指定货

```sql
WITH ...
SELECT
  DATE_FORMAT(b.job_date, '%Y-%m') AS month,
  b.client_name,
  SUM(CASE WHEN b.is_freehand=1 THEN 1 ELSE 0 END) AS freehand_cnt,
  SUM(CASE WHEN b.is_freehand=0 THEN 1 ELSE 0 END) AS nomi_cnt,
  ROUND(SUM(COALESCE(b.income,0)),2) AS income,
  ROUND(SUM(COALESCE(b.profit,0)),2) AS profit
FROM booking_latest b
WHERE b.job_date BETWEEN :start_date AND :end_date
  AND b.pro2_system_id IN (:company_ids)
GROUP BY month, b.client_name
QUALIFY ROW_NUMBER() OVER(PARTITION BY month ORDER BY SUM(b.profit) DESC) <= :top_n
ORDER BY month DESC, profit DESC;
```

---

## 5️⃣ 指定货来源代理贡献分析

**业务场景**: 分析指定货代理商的业务贡献

```sql
WITH ...
SELECT
  DATE_FORMAT(b.job_date, '%Y-%m') AS month,
  b.nomi_agent_name AS agent,
  COUNT(*) AS bookings,
  ROUND(SUM(COALESCE(b.lcl_rt,0)),2) AS rt,
  ROUND(SUM(COALESCE(b.profit,0)),2) AS profit
FROM booking_latest b
WHERE b.job_date BETWEEN :start_date AND :end_date
  AND b.pro2_system_id IN (:company_ids)
  AND b.is_freehand = 0
GROUP BY month, agent
ORDER BY month DESC, profit DESC;
```

---

## 6️⃣ 海外代理效果对比（Job vs Bill）

**业务场景**: 对比Job级别和Bill级别的海外代理效果

```sql
WITH ...
SELECT
  DATE_FORMAT(b.job_date, '%Y-%m') AS month,
  COALESCE(b.bl_handling_agent, 'N/A') AS bl_agent,
  COALESCE(j.job_handling_agent, 'N/A') AS job_agent,
  COUNT(*) AS bookings,
  ROUND(SUM(COALESCE(b.profit,0)),2) AS profit
FROM booking_latest b
LEFT JOIN job_latest j
  ON b.job_id=j.job_id AND b.pro2_system_id=j.pro2_system_id
WHERE b.job_date BETWEEN :start_date AND :end_date
  AND b.pro2_system_id IN (:company_ids)
GROUP BY month, bl_agent, job_agent
ORDER BY month DESC, profit DESC;
```

---

## 7️⃣ 航线盈利分析（含转运利润）

**业务场景**: 分析各航线的盈利表现，包含转运业务利润

```sql
WITH ...
SELECT
  DATE_FORMAT(j.job_date, '%Y-%m') AS month,
  CONCAT(j.pol_code,'-',j.pod_code) AS route,
  SUM(COALESCE(j.bk_count,0))       AS tickets,
  ROUND(SUM(COALESCE(j.rt,0)),2)    AS rt,
  ROUND(SUM(COALESCE(j.teu,0)),2)   AS teu,
  ROUND(SUM(COALESCE(j.profit,0)),2) AS profit,
  ROUND(SUM(COALESCE(j.transhipment_profit,0)),2) AS transhipment_profit
FROM job_latest j
WHERE j.job_date BETWEEN :start_date AND :end_date
  AND j.pro2_system_id IN (:company_ids)
GROUP BY month, route
ORDER BY month DESC, profit DESC;
```

---

## 8️⃣ 转运业务贡献分析（仅香港 852）

**业务场景**: 专门分析香港公司的转运业务贡献

```sql
WITH ...
SELECT
  DATE_FORMAT(b.job_date, '%Y-%m') AS month,
  b.is_transhipment,
  COUNT(*) AS bookings,
  ROUND(SUM(COALESCE(b.profit,0)),2) AS profit,
  ROUND(SUM(COALESCE(b.transhipment_profit,0)),2) AS transhipment_profit
FROM booking_latest b
WHERE b.job_date BETWEEN :start_date AND :end_date
  AND b.pro2_system_id = 852
GROUP BY month, b.is_transhipment
ORDER BY month DESC, transhipment_profit DESC;
```

---

## 9️⃣ "SIN 卸货+继续前送"链路分析

**业务场景**: 分析新加坡中转业务的后续配送情况

```sql
WITH ...
, sin_jobs AS (
  SELECT DISTINCT j.job_id, j.pro2_system_id
  FROM job_latest j
  WHERE j.pod_code = 'SIN'
    AND j.job_date BETWEEN :start_date AND :end_date
    AND j.pro2_system_id IN (:company_ids)
)
SELECT
  DATE_FORMAT(b.job_date, '%Y-%m') AS month,
  b.bill_pod AS final_pod,
  COUNT(*) AS bookings,
  ROUND(SUM(COALESCE(b.profit,0)),2) AS profit
FROM booking_latest b
JOIN sin_jobs sj
  ON b.job_id=sj.job_id AND b.pro2_system_id=sj.pro2_system_id
WHERE COALESCE(b.bill_pod,'') <> 'SIN'
GROUP BY month, final_pod
ORDER BY month DESC, profit DESC;
```

---

## 🔟 销售部门/营业员贡献分析

**业务场景**: 按部门关键字分析销售团队贡献

```sql
WITH ...
SELECT
  DATE_FORMAT(b.job_date, '%Y-%m') AS month,
  COALESCE(b.salesman_department,'N/A') AS dept,
  COALESCE(b.salesman_name,'N/A') AS salesman,
  COUNT(*) AS bookings,
  ROUND(SUM(COALESCE(b.profit,0)),2) AS profit
FROM booking_latest b
WHERE b.job_date BETWEEN :start_date AND :end_date
  AND b.pro2_system_id IN (:company_ids)
  AND (b.salesman_department LIKE '%销售%' OR b.salesman_department LIKE '%营业%' OR b.salesman_department LIKE '%市场%')
GROUP BY month, dept, salesman
ORDER BY month DESC, profit DESC;
```

---

## 1️⃣1️⃣ 异常波动监测

**业务场景**: 环比监测票数±20%，利润±30%的异常波动

```sql
WITH ...
, m AS (
  SELECT
    DATE_FORMAT(b.job_date, '%Y-%m') AS ym,
    b.client_name,
    SUM(1) AS bookings,
    SUM(COALESCE(b.profit,0)) AS profit
  FROM booking_latest b
  WHERE b.job_date BETWEEN :hist_start_date AND :end_date
    AND b.pro2_system_id IN (:company_ids)
  GROUP BY ym, b.client_name
),
z AS (
  SELECT
    ym, client_name, bookings, profit,
    LAG(bookings) OVER (PARTITION BY client_name ORDER BY ym) AS prev_bookings,
    LAG(profit)   OVER (PARTITION BY client_name ORDER BY ym) AS prev_profit
  FROM m
)
SELECT
  ym, client_name,
  bookings, prev_bookings,
  profit, prev_profit,
  ROUND(100.0*(bookings - prev_bookings)/NULLIF(prev_bookings,0),1) AS bookings_pct,
  ROUND(100.0*(profit - prev_profit)/NULLIF(prev_profit,0),1)       AS profit_pct
FROM z
WHERE (ABS(bookings - prev_bookings)/NULLIF(prev_bookings,0) >= 0.20)
   OR (ABS(profit   - prev_profit)/NULLIF(prev_profit,0)   >= 0.30)
ORDER BY ym DESC, ABS(profit_pct) DESC NULLS LAST;
```

---

## 1️⃣2️⃣ 自揽/指定占比分析

**业务场景**: 按业务模式分析自揽货与指定货的占比

```sql
WITH ...
SELECT
  DATE_FORMAT(b.job_date, '%Y-%m') AS month,
  CASE
    WHEN b.service_mode=1 AND j.is_consolidation=1 THEN 'LCL-Consol'
    WHEN b.service_mode=1 AND j.is_consolidation=0 THEN 'LCL-Coload'
    WHEN b.service_mode=2 THEN 'FCL'
    WHEN b.service_mode=4 THEN 'AIR'
    ELSE 'Other'
  END AS biz_mode,
  SUM(CASE WHEN b.is_freehand=1 THEN 1 ELSE 0 END) AS freehand_cnt,
  SUM(CASE WHEN b.is_freehand=0 THEN 1 ELSE 0 END) AS nomi_cnt
FROM booking_latest b
LEFT JOIN job_latest j
  ON b.job_id=j.job_id AND b.pro2_system_id=j.pro2_system_id
WHERE b.job_date BETWEEN :start_date AND :end_date
  AND b.pro2_system_id IN (:company_ids)
GROUP BY month, biz_mode
ORDER BY month DESC, nomi_cnt DESC;
```

---

## 📊 可视化规范建议

**推荐技术栈**: AntV/G2 家族

**设计原则**:
- 先表后图，确保数据可读性
- 字段数量 ≤ 4，避免信息过载
- 并列柱状图不叠加，保持清晰度
- 重要信息置顶，突出关键指标

**常用图表类型**:
- 📈 **趋势分析**: 折线图、面积图
- 📊 **对比分析**: 柱状图、条形图
- 🥧 **占比分析**: 饼图、环形图
- 📋 **明细数据**: 表格、列表

**交互建议**:
- 支持时间范围筛选
- 提供钻取功能
- 添加数据导出选项
- 实现响应式布局
```
