# SSH配置修复总结

## 🎯 修复完成情况

✅ **已完成**: 
1. 全部使用阿里云PyPI源
2. 修复所有scripts目录下脚本的SSH配置问题

## 📋 修复内容

### 1. PyPI源统一修改

**文件**: `pyproject.toml`
- **修改前**: 使用清华大学PyPI镜像 `https://pypi.tuna.tsinghua.edu.cn/simple`
- **修改后**: 使用阿里云PyPI镜像 `https://mirrors.aliyun.com/pypi/simple/`
- **原因**: 阿里云镜像稳定性更好，特别是在阿里云服务器上

### 2. SSH配置修复

#### 修复的脚本文件：

**✅ scripts/check_all_services.sh**
- 添加HK_SSH_KEY变量
- 添加get_ssh_cmd函数
- 修复所有SSH调用以支持HK服务器的密钥认证

**✅ scripts/transfer_fixed.sh**
- 更新HK_SSH_KEY路径
- 已有SSH命令获取函数，只需更新密钥路径

**✅ scripts/reinit.sh**
- 添加HK_SSH_KEY变量
- 添加get_ssh_cmd和get_rsync_ssh_cmd函数
- 修复所有SSH调用（连接检查、停止服务、文件传输、环境重建、启动服务、状态检查）

**✅ scripts/start_all_services.sh**
- 添加HK_SSH_KEY变量
- 添加get_ssh_cmd函数
- 修复启动服务逻辑以支持HK服务器

**✅ scripts/check_service_logs.sh**
- 完全重写，添加SSH命令获取函数
- 支持HK服务器的密钥认证

**✅ scripts/check_sha_service.sh**
- 完全重写，添加SSH命令获取函数
- 支持所有服务器的正确认证方式

**✅ scripts/stop_all_services_simple.sh** (新建)
- 创建简化版本的停止服务脚本
- 支持所有服务器的正确SSH认证
- 原复杂版本已备份

**✅ update** (软链接文件)
- 更新HK_SSH_KEY路径
- 修复SHA服务器地址

### 3. 服务器配置更新

**所有脚本中的HK服务器配置**:
- **服务器地址**: `<EMAIL>`
- **SSH密钥**: `/Users/<USER>/Documents/pem-files/<EMAIL>`
- **远程路径**: `/root/mcp-cms`
- **认证方式**: SSH密钥认证（其他服务器使用密码认证）

## 🔧 SSH认证策略

### HK服务器 (hkg)
- **认证方式**: SSH密钥
- **命令格式**: `ssh -i /Users/<USER>/Documents/pem-files/<EMAIL> -o StrictHostKeyChecking=no`

### 其他服务器 (qd, sha, tyo)
- **认证方式**: 密码认证
- **命令格式**: `sshpass -p "2929!lxj#LXJ" ssh -o StrictHostKeyChecking=no`

## 📊 测试结果

### ✅ 服务状态检查
```
✅ [qd] 服务正在运行 (<EMAIL>)
✅ [sha] 服务正在运行 (<EMAIL>)  
✅ [tyo] 服务正在运行 (<EMAIL>)
✅ [hkg] 服务正在运行 (<EMAIL>)
```

### ✅ PyPI源修复
- HK阿里云服务器现在可以正常执行 `uv sync`
- 使用阿里云PyPI镜像，网络访问稳定

## 🛠️ 可用的管理命令

### 检查所有服务状态
```bash
./scripts/check_all_services.sh
```

### 部署代码到所有服务器
```bash
./update
```

### 启动/停止服务
```bash
./scripts/start_all_services.sh
./scripts/stop_all_services_simple.sh
```

### 检查特定服务器
```bash
./scripts/check_sha_service.sh
./scripts/check_service_logs.sh
```

### 重新部署
```bash
./scripts/reinit.sh
```

## 📁 备份文件

以下原始文件已备份：
- `scripts/stop_all_services.sh.backup`
- `scripts/check_service_logs.sh.backup`
- `scripts/check_sha_service.sh.backup`

## 🔄 更新历史

- **2025-08-07**: 
  - 统一使用阿里云PyPI源
  - 修复所有scripts目录下脚本的SSH配置
  - 创建简化版本的停止服务脚本
  - 所有4个服务器现在都可以正常管理

## 📝 注意事项

1. **PyPI源**: 现在所有服务器都使用阿里云PyPI镜像
2. **SSH认证**: HK服务器使用密钥认证，其他服务器使用密码认证
3. **脚本兼容性**: 所有脚本现在都支持混合认证方式
4. **备份策略**: 重要脚本修改前都已备份

## 🎉 总结

所有SSH配置问题已修复，PyPI源已统一为阿里云镜像。现在可以：
- ✅ 正常部署代码到所有4个服务器
- ✅ 正常检查所有服务器状态
- ✅ 正常启动/停止所有服务器上的服务
- ✅ HK阿里云服务器可以正常执行uv sync
- ✅ 所有脚本都支持正确的SSH认证方式
