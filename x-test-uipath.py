# 测试uipath_code/data_processing_inv_cn.py

import json
from utils.uipath_code.data_processing_inv_cn import (
    get_update_inv_list,
    get_last_inv_cn_data,
    update_internal_remarks,
    update_blob_text_field_safe,
    send_email_to_manager
)

if __name__ == "__main__":
    # 测试发送邮件

    application_id = 1
    application_datetime = '2025-01-01'
    job_file_no = 'JOB001'
    attn = '测试管理员'
    to_list = ['<EMAIL>', '<EMAIL>']
    mail_content = '这是一封测试邮件，请注意检查内容是否准确。'
    send_email_to_manager(application_id, application_datetime, job_file_no, mail_content, attn, to_list)