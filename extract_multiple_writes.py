#!/usr/bin/env python3
"""
提取因更改被多次写入的数据工具
从 t_booking_details 和 t_job_details 表中识别并导出重复记录
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any
import pandas as pd

# 设置项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.basic.data_conn_unified import get_mysql_connection
from utils.basic.optimized_export import export_both_formats

async def find_multiple_writes_data():
    """
    查找因更改被多次写入的数据
    返回booking和job表中的重复记录统计
    """
    results = {
        'booking_duplicates': [],
        'job_duplicates': [],
        'booking_history': [],
        'job_history': []
    }
    
    try:
        async with get_mysql_connection('mcp_tokens') as conn:
            async with conn.cursor() as cursor:
                print("正在分析 t_booking_details 表中的重复数据...")
                
                # 查找booking表中有多个版本的数据
                await cursor.execute("""
                    SELECT 
                        job_no,
                        bkbl_no,
                        COUNT(*) as write_count,
                        MIN(created_at) as first_write,
                        MAX(created_at) as last_write,
                        GROUP_CONCAT(DISTINCT data_hash ORDER BY created_at) as all_hashes,
                        GROUP_CONCAT(DISTINCT session_id ORDER BY created_at) as all_sessions
                    FROM t_booking_details 
                    WHERE job_no IS NOT NULL AND bkbl_no IS NOT NULL
                    GROUP BY job_no, bkbl_no
                    HAVING COUNT(*) > 1
                    ORDER BY write_count DESC, last_write DESC
                    LIMIT 1000
                """)
                
                booking_duplicates = await cursor.fetchall()
                print(f"发现 {len(booking_duplicates)} 个订舱记录有多次写入")
                
                # 转换为字典格式
                for row in booking_duplicates:
                    results['booking_duplicates'].append({
                        'job_no': row[0],
                        'bkbl_no': row[1],
                        'write_count': row[2],
                        'first_write': row[3],
                        'last_write': row[4],
                        'all_hashes': row[5],
                        'all_sessions': row[6],
                        'time_span_hours': (row[4] - row[3]).total_seconds() / 3600 if row[4] and row[3] else 0
                    })
                
                print("正在分析 t_job_details 表中的重复数据...")
                
                # 查找job表中有多个版本的数据
                await cursor.execute("""
                    SELECT 
                        job_no,
                        COUNT(*) as write_count,
                        MIN(created_at) as first_write,
                        MAX(created_at) as last_write,
                        GROUP_CONCAT(DISTINCT data_hash ORDER BY created_at) as all_hashes,
                        GROUP_CONCAT(DISTINCT session_id ORDER BY created_at) as all_sessions
                    FROM t_job_details 
                    WHERE job_no IS NOT NULL
                    GROUP BY job_no
                    HAVING COUNT(*) > 1
                    ORDER BY write_count DESC, last_write DESC
                    LIMIT 1000
                """)
                
                job_duplicates = await cursor.fetchall()
                print(f"发现 {len(job_duplicates)} 个作业记录有多次写入")
                
                # 转换为字典格式
                for row in job_duplicates:
                    results['job_duplicates'].append({
                        'job_no': row[0],
                        'write_count': row[1],
                        'first_write': row[2],
                        'last_write': row[3],
                        'all_hashes': row[4],
                        'all_sessions': row[5],
                        'time_span_hours': (row[3] - row[2]).total_seconds() / 3600 if row[3] and row[2] else 0
                    })
                
                # 获取一些重复记录的详细历史数据（取前10个最严重的）
                if results['booking_duplicates']:
                    print("正在获取订舱记录的详细历史...")
                    top_booking_duplicates = results['booking_duplicates'][:10]
                    
                    for dup in top_booking_duplicates:
                        await cursor.execute("""
                            SELECT 
                                id, session_id, analysis_timestamp, created_at, 
                                job_no, bkbl_no, client_name, vessel, voyage,
                                service_mode, income, cost, profit, 
                                salesman_name, operator_name, data_hash
                            FROM t_booking_details 
                            WHERE job_no = %s AND bkbl_no = %s
                            ORDER BY created_at
                        """, (dup['job_no'], dup['bkbl_no']))
                        
                        history_records = await cursor.fetchall()
                        for record in history_records:
                            results['booking_history'].append({
                                'id': record[0],
                                'session_id': record[1],
                                'analysis_timestamp': record[2],
                                'created_at': record[3],
                                'job_no': record[4],
                                'bkbl_no': record[5],
                                'client_name': record[6],
                                'vessel': record[7],
                                'voyage': record[8],
                                'service_mode': record[9],
                                'income': float(record[10]) if record[10] else None,
                                'cost': float(record[11]) if record[11] else None,
                                'profit': float(record[12]) if record[12] else None,
                                'salesman_name': record[13],
                                'operator_name': record[14],
                                'data_hash': record[15]
                            })
                
                if results['job_duplicates']:
                    print("正在获取作业记录的详细历史...")
                    top_job_duplicates = results['job_duplicates'][:10]
                    
                    for dup in top_job_duplicates:
                        await cursor.execute("""
                            SELECT 
                                id, session_id, analysis_timestamp, created_at,
                                job_no, vessel, voyage, pol_code, pod_code,
                                bk_count, rt, teu, income, cost, profit,
                                operator_name, data_hash
                            FROM t_job_details 
                            WHERE job_no = %s
                            ORDER BY created_at
                        """, (dup['job_no'],))
                        
                        history_records = await cursor.fetchall()
                        for record in history_records:
                            results['job_history'].append({
                                'id': record[0],
                                'session_id': record[1],
                                'analysis_timestamp': record[2],
                                'created_at': record[3],
                                'job_no': record[4],
                                'vessel': record[5],
                                'voyage': record[6],
                                'pol_code': record[7],
                                'pod_code': record[8],
                                'bk_count': record[9],
                                'rt': float(record[10]) if record[10] else None,
                                'teu': float(record[11]) if record[11] else None,
                                'income': float(record[12]) if record[12] else None,
                                'cost': float(record[13]) if record[13] else None,
                                'profit': float(record[14]) if record[14] else None,
                                'operator_name': record[15],
                                'data_hash': record[16]
                            })
        
        return results
        
    except Exception as e:
        print(f"查找重复数据时出错: {e}")
        raise

async def export_multiple_writes_to_excel():
    """
    导出多次写入的数据到Excel
    """
    try:
        print("开始查找多次写入的数据...")
        data = await find_multiple_writes_data()
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 准备导出数据
        export_results = {}
        
        # 1. 订舱重复记录统计
        if data['booking_duplicates']:
            booking_df = pd.DataFrame(data['booking_duplicates'])
            booking_export = {
                'data': booking_df.to_dict('records'),
                'total_count': len(booking_df)
            }
            filename = f"订舱重复记录统计_{timestamp}"
            booking_urls = await export_both_formats(booking_export, filename, "analysis")
            export_results['booking_duplicates'] = booking_urls
            print(f"✅ 订舱重复记录统计已导出: {len(data['booking_duplicates'])} 条记录")
        
        # 2. 作业重复记录统计
        if data['job_duplicates']:
            job_df = pd.DataFrame(data['job_duplicates'])
            job_export = {
                'data': job_df.to_dict('records'),
                'total_count': len(job_df)
            }
            filename = f"作业重复记录统计_{timestamp}"
            job_urls = await export_both_formats(job_export, filename, "analysis")
            export_results['job_duplicates'] = job_urls
            print(f"✅ 作业重复记录统计已导出: {len(data['job_duplicates'])} 条记录")
        
        # 3. 订舱详细历史记录
        if data['booking_history']:
            history_df = pd.DataFrame(data['booking_history'])
            history_export = {
                'data': history_df.to_dict('records'),
                'total_count': len(history_df)
            }
            filename = f"订舱详细历史记录_{timestamp}"
            history_urls = await export_both_formats(history_export, filename, "analysis")
            export_results['booking_history'] = history_urls
            print(f"✅ 订舱详细历史记录已导出: {len(data['booking_history'])} 条记录")
        
        # 4. 作业详细历史记录
        if data['job_history']:
            job_history_df = pd.DataFrame(data['job_history'])
            job_history_export = {
                'data': job_history_df.to_dict('records'),
                'total_count': len(job_history_df)
            }
            filename = f"作业详细历史记录_{timestamp}"
            job_history_urls = await export_both_formats(job_history_export, filename, "analysis")
            export_results['job_history'] = job_history_urls
            print(f"✅ 作业详细历史记录已导出: {len(data['job_history'])} 条记录")
        
        # 汇总统计信息
        summary = {
            'booking_duplicate_count': len(data['booking_duplicates']),
            'job_duplicate_count': len(data['job_duplicates']),
            'booking_history_count': len(data['booking_history']),
            'job_history_count': len(data['job_history']),
            'export_timestamp': timestamp
        }
        
        # 导出汇总信息
        summary_export = {
            'data': [summary],
            'total_count': 1
        }
        filename = f"重复数据汇总统计_{timestamp}"
        summary_urls = await export_both_formats(summary_export, filename, "analysis")
        export_results['summary'] = summary_urls
        
        print("\n" + "="*60)
        print("📊 重复数据分析结果:")
        print(f"   订舱表重复记录: {summary['booking_duplicate_count']} 个")
        print(f"   作业表重复记录: {summary['job_duplicate_count']} 个")
        print(f"   订舱历史详情: {summary['booking_history_count']} 条")
        print(f"   作业历史详情: {summary['job_history_count']} 条")
        print("="*60)
        
        print("\n📁 导出文件下载链接:")
        for category, urls in export_results.items():
            category_names = {
                'booking_duplicates': '订舱重复记录统计',
                'job_duplicates': '作业重复记录统计', 
                'booking_history': '订舱详细历史记录',
                'job_history': '作业详细历史记录',
                'summary': '重复数据汇总统计'
            }
            print(f"\n{category_names.get(category, category)}:")
            for format_type, url in urls.items():
                print(f"  {format_type.upper()}: {url}")
        
        return export_results
        
    except Exception as e:
        print(f"导出多次写入数据时出错: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    asyncio.run(export_multiple_writes_to_excel())