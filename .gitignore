# 忽略日志文件
logs/

# 忽略所有的 .log 文件
*.log

# 忽略 node_modules 目录（常见于 Node.js 项目）
node_modules/

# 忽略 macOS 系统生成的文件
.DS_Store

# 忽略编译生成的文件夹
dist/
build/

# 忽略临时文件
*.tmp
*.swp

# 忽略环境配置文件（包含敏感信息）
.env
.env.*

# 忽略虚拟环境
.venv/
venv/
env/

# 忽略 pytest 缓存
.pytest_cache/

# 忽略 Jupyter Notebook 的输出和检查点
.ipynb_checkpoints/
*.ipynb_output

# 忽略特定的脚本和可执行文件
*.sh
*.pyc
*.pyo
*.exe
*.dll
*.so
*.dylib
*.egg-info/
*.egg
*.whl
*.pyc
*.pyo