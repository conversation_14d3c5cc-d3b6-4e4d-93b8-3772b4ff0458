# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MCP-CMS is an enterprise content management system MCP (Model Context Protocol) server that provides data export functionality for job details and booking details from shipping/logistics databases. The system is built with Python FastAPI and includes automated data extraction, caching, and export capabilities.

## Development Commands

### Dependencies and Setup
```bash
# Install dependencies (uses Tsinghua PyPI mirror)
uv sync

# Install a new dependency
uv add package_name
```

### Main Server
```bash
# Start the MCP server (default: auto-extract disabled)
python mcp_server_cms.py

# Start with auto-extract enabled
python mcp_server_cms.py --enable-auto-extract

# Run with UV (recommended)
uv run mcp_server_cms.py
```

### Testing and Debugging
```bash
# Clear scheduler data tables for testing
python clear_scheduler_tables.py

# Run analysis completion test
python x-finish-analysis.py

# Test specific modules (examples from codebase)
python -m utils.basic.profit_data_scheduler
python -m utils.database.db_mysql_analysis
```

### Service Management Scripts
```bash
# Located in scripts/ directory - use from project root:

# Daily code updates (most common)
./scripts/transfer_fixed.sh

# Quick redeployment
./scripts/reinit.sh

# Check all services status
./scripts/check_all_services.sh

# Start/stop all services
./scripts/start_all_services.sh
./scripts/stop_all_services.sh

# Check service logs
./scripts/check_service_logs.sh
```

### Python Dependencies
- Uses `uv` for dependency management with Tsinghua PyPI mirror
- Main dependencies: FastAPI, aiomysql, pandas, pymysql, anthropic, fastapi-mcp, firebirdsql
- Configuration via `pyproject.toml`
- Requires Python >=3.12

## Architecture Overview

### Core Components

1. **MCP Server Entry Point** (`mcp_server_cms.py`)
   - Main application entry with command-line argument parsing
   - Configures FastAPI with MCP protocol support
   - Handles authentication via MCP tokens
   - Manages server lifecycle and graceful shutdown

2. **FastAPI Application** (`utils/fastapi_apps/fastapi_cms_simplified.py`)
   - Simplified CMS application focused on data export
   - RESTful endpoints for job/booking data export
   - Health monitoring and system status endpoints

3. **Database Layer** (`utils/database/`)
   - `db_pro2_basic.py`: Main database operations wrapper
   - `db_mysql_analysis.py`: MySQL analysis operations
   - Connection pooling and transaction management

4. **Data Processing** (`utils/basic/`)
   - `profit_data_scheduler.py`: Automated data extraction scheduler
   - `data_cache_manager.py`: Caching system for performance
   - `optimized_export.py`: Export functionality (Excel/CSV)
   - `db_pro2_sea_air_profit.py`: Core business logic for data retrieval

5. **Configuration Management**
   - `config.py`: Centralized configuration with time-based execution windows
   - Environment-based settings with `.env` file support
   - Multi-server deployment configuration

### Key Features

- **MCP Protocol Integration**: Fully integrated MCP server with FastAPI
- **Multi-Database Support**: Firebird (source) and MySQL (cache/tokens)
- **Automated Data Extraction**: Scheduled extraction of historical data
- **Export Capabilities**: Excel/CSV export with column mapping
- **Caching System**: Intelligent caching for performance optimization
- **Multi-Server Deployment**: Scripts support deployment to 4 servers (QD, SHA, TYO, HKG)

### Data Flow

1. **Source Data**: Firebird databases containing job/booking details
2. **Extraction**: Automated scheduler extracts data based on time windows
3. **Processing**: Data cleaning, transformation, and caching
4. **Storage**: Processed data stored in MySQL for fast access
5. **Export**: On-demand export to Excel/CSV via MCP tools

### Authentication & Security

- MCP token-based authentication required for all operations
- Token validation via `utils/basic/get_mcp_tokens.py`
- Secure database connections with SSH tunneling support
- Access control and permission verification

### Scheduler System

The profit data scheduler (`profit_data_scheduler.py`) runs automated extraction:
- **Time Windows**: Daily 20:00-08:00 execution window
- **Tiered Strategy**: Different frequencies based on data age
  - Recent data (1-2 months): Daily checks
  - Older data (3+ months): Weekly/monthly checks
- **Change Detection**: Only processes data that has actually changed
- **Historical Range**: Processes data from 2020 onwards

### Deployment Architecture

- **Development**: Local machine with direct database connections
- **Production**: 4 server deployment (青岛/上海/东京/香港)
- **Service Management**: systemd services with auto-restart
- **Monitoring**: Prometheus metrics on port +100 from main port
- **Logs**: Structured logging with rotation

## Important File Locations

- **Main server**: `mcp_server_cms.py`
- **Core business logic**: `utils/basic/db_pro2_sea_air_profit.py`
- **Export functionality**: `utils/basic/optimized_export.py`
- **Database operations**: `utils/database/db_pro2_basic.py`
- **Configuration**: `config.py`
- **Deployment scripts**: `scripts/` directory
- **Documentation**: `docs/` directory with feature guides

## Environment Configuration

- Uses `.env` files for sensitive configuration (database credentials, API keys)
- Server-specific configurations via `server_config.conf` (example provided)
- Configuration class in `config.py` with environment variable support
- Key configuration areas:
  - Database connections (Firebird and MySQL)
  - Email notifications (SMTP settings)
  - Connection pool sizes and timeouts
  - Logging levels and file paths
  - Scheduler time windows and intervals

## Testing & Debugging

- Clear test data tables: `python clear_scheduler_tables.py`
- Analysis completion: `python x-finish-analysis.py`
- Service status monitoring via shell scripts
- Comprehensive logging system with multiple log levels

## MCP Protocol Integration

The system uses the Model Context Protocol (MCP) for AI agent integration:
- MCP server wrapping the FastAPI application via `fastapi-mcp`
- Token-based authentication for MCP clients
- Automatic tool registration for data export and analysis functions
- Prometheus metrics exposed on port +100 from main server port
- Graceful shutdown handling with proper cleanup of database connections