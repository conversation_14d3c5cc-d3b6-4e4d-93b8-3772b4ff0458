# HK阿里云服务器设置总结

## 🎯 任务完成情况

✅ **已完成**: HK阿里云服务器的systemd服务初始化和PyPI源问题修复

## 📋 服务器信息

- **服务器地址**: `<EMAIL>`
- **SSH密钥**: `/Users/<USER>/Documents/pem-files/<EMAIL>`
- **远程路径**: `/root/mcp-cms`
- **服务名称**: `mcp-cms`

## 🔧 修改的文件

### 1. 主要配置文件
- `setup_hk_service.sh` - 更新为新的阿里云服务器配置
- `server_config.conf.example` - 更新HKG服务器地址
- `scripts/DEPLOYMENT_GUIDE.md` - 更新服务器列表

### 2. 部署脚本更新
- `scripts/check_all_services.sh` - 添加HK服务器SSH密钥支持
- `scripts/transfer_fixed.sh` - 更新SSH密钥路径
- `scripts/reinit.sh` - 更新服务器地址
- `scripts/start_all_services.sh` - 更新服务器地址
- `scripts/stop_all_services.sh` - 更新服务器地址
- `update` - 更新SSH密钥路径和服务器地址

### 3. 新建文件
- `setup_hk_aliyun_service.sh` - 专门的HK阿里云服务器初始化脚本
- `fix_hk_pypi_source.sh` - PyPI源问题修复脚本
- `pyproject.hk.toml` - HK服务器专用配置文件
- `test_hk_connection.sh` - 连接测试脚本

## 🚨 遇到的问题及解决方案

### 问题1: SSH连接失败
**原因**: 脚本中使用了旧的AWS服务器配置
**解决**: 更新所有脚本中的SSH密钥路径和服务器地址

### 问题2: uv sync失败 (403 Forbidden)
**原因**: 阿里云服务器无法访问清华大学PyPI镜像源
**解决**: 
- 创建修复脚本 `fix_hk_pypi_source.sh`
- 将PyPI源改为阿里云镜像: `https://mirrors.aliyun.com/pypi/simple/`
- 使用完整的uv路径: `~/.local/bin/uv`

### 问题3: 服务状态检查失败
**原因**: 检查脚本对所有服务器都使用密码认证
**解决**: 修改 `check_all_services.sh`，为HK服务器使用SSH密钥认证

## 📊 当前状态

所有4个服务器的MCP-CMS服务都在正常运行：

```
✅ [qd] 服务正在运行 (<EMAIL>)
✅ [sha] 服务正在运行 (<EMAIL>)  
✅ [tyo] 服务正在运行 (<EMAIL>)
✅ [hkg] 服务正在运行 (<EMAIL>)
```

## 🛠️ 常用管理命令

### 检查所有服务状态
```bash
./scripts/check_all_services.sh
```

### 部署代码到所有服务器
```bash
./update
```

### 仅部署到HK服务器（如需要）
```bash
# 手动SSH连接
ssh -i /Users/<USER>/Documents/pem-files/<EMAIL> <EMAIL>

# 查看服务状态
ssh -i /Users/<USER>/Documents/pem-files/<EMAIL> <EMAIL> 'systemctl status mcp-cms'

# 查看服务日志
ssh -i /Users/<USER>/Documents/pem-files/<EMAIL> <EMAIL> 'journalctl -u mcp-cms -f'
```

### 重新初始化HK服务器systemd服务
```bash
./setup_hk_aliyun_service.sh
```

### 修复PyPI源问题（如再次遇到）
```bash
./fix_hk_pypi_source.sh
```

## 📝 注意事项

1. **PyPI源配置**: HK阿里云服务器使用阿里云PyPI镜像，其他服务器使用清华镜像
2. **SSH认证**: HK服务器使用SSH密钥认证，其他服务器使用密码认证
3. **服务路径**: HK服务器使用 `/root/mcp-cms`，其他服务器使用不同路径
4. **备份文件**: 原始配置已备份为 `pyproject.toml.backup`

## 🔄 更新历史

- **2025-08-07**: 完成HK阿里云服务器初始化和PyPI源修复
- **服务器变更**: 从 `<EMAIL>` 迁移到 `<EMAIL>`
