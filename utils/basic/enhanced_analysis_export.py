#!/usr/bin/env python3
"""
增强版分析数据导出工具
支持处理优化后的分析和提取函数的统一返回格式
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, Any, List
import pandas as pd

# 设置项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .optimized_export import export_to_oss, export_both_formats
from utils.database.db_mysql_analysis import (
    analysis_booking_by_month_data,
    analysis_job_by_month_data,
    analysis_booking_by_customer,
    analysis_booking_by_top_n_profit_customers,
    analysis_booking_by_top_n_bl_customers,
    analysis_job_consol_line,
    analysis_booking_nomi_agents,
    analysis_booking_top_n_rt,
    get_sea_air_profit_from_tokens_table_cached,
    get_job_details_from_tokens_table_cached
)

def generate_friendly_filename(data_type: str, metadata: Dict[str, Any]) -> str:
    """
    根据数据类型和元数据生成用户友好的中文文件名
    
    Args:
        data_type: 数据类型标识
        metadata: 包含分析参数的元数据
        
    Returns:
        str: 用户友好的中文文件名前缀
    """
    # 获取当前时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 分公司代码映射
    company_map = {
        86532: "青岛QDO",
        86021: "上海SHA", 
        852: "香港HKG",
        8103: "东京TKY"
    }
    
    # 获取分公司信息
    pro2_system_id = metadata.get('pro2_system_id')
    company_str = company_map.get(pro2_system_id, "全部分公司") if pro2_system_id else "全部分公司"
    
    # 根据数据类型生成友好文件名
    if data_type == "booking_monthly_analysis":
        month_count = metadata.get('month_count', '')
        return f"订舱业务月度分析_{company_str}_{month_count}个月_{timestamp}"
        
    elif data_type == "job_monthly_analysis":
        month_count = metadata.get('month_count', '')
        return f"作业明细月度分析_{company_str}_{month_count}个月_{timestamp}"
        
    elif data_type == "customer_analysis":
        customer_name = metadata.get('customer_name', '客户')
        month_count = metadata.get('month_count', '')
        return f"客户分析_{customer_name}_{company_str}_{month_count}个月_{timestamp}"
        
    elif data_type == "top_customers_analysis":
        top_n = metadata.get('top_n', '')
        month_count = metadata.get('month_count', '')
        return f"前{top_n}大客户分析_{company_str}_{month_count}个月_{timestamp}"
        
    elif data_type == "top_bl_customers_analysis":
        top_n = metadata.get('top_n', '')
        month_count = metadata.get('month_count', '')
        return f"前{top_n}大业务票数客户分析_{company_str}_{month_count}个月_{timestamp}"
        
    elif data_type == "consol_line_analysis":
        month_count = metadata.get('month_count', '')
        return f"集拼航线分析_{company_str}_{month_count}个月_{timestamp}"
        
    elif data_type == "nomi_agents_analysis":
        top_n = metadata.get('top_n', '')
        month_count = metadata.get('month_count', '')
        return f"前{top_n}大指定货代理分析_{company_str}_{month_count}个月_{timestamp}"
        
    elif data_type == "top_rt_ports_analysis":
        top_n = metadata.get('top_n', '')
        month_count = metadata.get('month_count', '')
        return f"前{top_n}大RT目的港分析_{company_str}_{month_count}个月_{timestamp}"
        
    elif data_type == "booking_details_extract":
        query_period = metadata.get('query_period', '')
        period_str = query_period.replace(' to ', '_至_') if query_period else ''
        return f"订舱明细数据提取_{company_str}_{period_str}_{timestamp}"
        
    elif data_type == "job_details_extract":
        query_period = metadata.get('query_period', '')
        period_str = query_period.replace(' to ', '_至_') if query_period else ''
        return f"作业明细数据提取_{company_str}_{period_str}_{timestamp}"
        
    else:
        # 默认文件名
        return f"数据分析_{data_type}_{company_str}_{timestamp}"

# 分析函数的中文列映射 - 用于DataFrame转换为记录格式
ANALYSIS_COLUMN_MAPPINGS = {
    "booking_details_extract": {
        "job_type_cn": "业务类型",
        "job_date": "工作档日期",
        "job_no": "工作档编号",
        "bkbl_no": "订舱提单编号",
        "client_name": "客户名称",
        "vessel": "船名",
        "voyage": "航次",
        "job_pol": "工作档起运地",
        "bill_pol": "提单起运地",
        "bill_pod": "提单卸货地",
        "service_mode": "服务模式",
        "lcl_rt": "拼箱RT",
        "teu": "TEU",
        "air_weight": "空运重量",
        "income": "收入",
        "cost": "成本",
        "profit": "利润",
        "transhipment_profit": "转运利润",
        "total_business_profit": "总业务利润",
        "is_freehand": "是否自揽货",
        "salesman_name": "业务员",
        "salesman_department": "营业员部门",
        "nomi_agent_name": "指定货代理",
        "operator_name": "操作员",
        "operator_department": "操作部门",
        "coloader_name": "Coloader名称",
        "job_handling_agent": "工作档代理",
        "bl_handling_agent": "提单代理",
        "is_transhipment": "是否转运",
        "pro2_system_id": "分公司代码"
    },
    "job_details_extract": {
        "job_type_cn": "业务类型",
        "job_date": "工作档日期",
        "job_no": "工作档编号",
        "vessel": "船名",
        "voyage": "航次",
        "pol_code": "起运港",
        "pod_code": "卸货港",
        "bk_count": "订舱数",
        "rt": "计费吨",
        "teu": "TEU",
        "income": "收入",
        "cost": "成本",
        "profit": "利润",
        "transhipment_profit": "转运利润",
        "total_business_profit": "总业务利润",
        "operator_name": "操作员",
        "job_handling_agent": "工作档代理",
        "nomi_count": "指定货票数",
        "nomi_rt": "指定货RT",
        "is_consolidation": "是否集拼",
        "bill_count": "提单数",
        "consolidation_20": "20集拼量",
        "consolidation_40": "40集拼量",
        "operator_department": "操作部门",
        "is_op_finished": "操作完成",
        "is_checked": "审核状态",
        "etd_date": "开船日期",
        "eta_date": "到港日期",
        "pro2_system_id": "分公司代码"
    },
    "booking_monthly_analysis": {
        "year": "年份",
        "month": "月份", 
        "pro2_system_id": "分公司代码",
        "business_type": "业务类型",
        "customer_count": "客户数量",
        "bkbl_count": "票数",
        "rt": "计费吨",
        "teu": "TEU",
        "air_weight": "空运重量",
        "income": "收入",
        "cost": "成本", 
        "profit": "利润",
        "profit_rate": "利润率(%)",
        "all_nominated_count": "指定货票数",
        "all_nominated_rt": "指定货RT",
        "all_nominated_profit": "指定货利润",
        "nomi_agent_count": "指定货代理数量"
    },
    "job_monthly_analysis": {
        "year": "年份",
        "month": "月份", 
        "pro2_system_id": "分公司代码",
        "business_type": "业务类型",
        "bkbl_count": "票数",
        "rt": "计费吨",
        "teu": "TEU",
        "income": "收入",
        "cost": "成本", 
        "profit": "利润",
        "profit_rate": "利润率(%)",
        "all_nominated_count": "指定货票数",
        "all_nominated_rt": "指定货RT",
        "all_nominated_teu": "指定货TEU",
        "consol_line_count": "集拼航线数",
        "consol_rt": "集拼RT",
        "consol_20_count": "20尺集拼量",
        "consol_40_count": "40尺集拼量"
    },
    "customer_analysis": {
        "year": "年份",
        "month": "月份",
        "pro2_system_id": "分公司代码",
        "customer_name": "客户名称",
        "pod_code": "卸货港代码",
        "bkbl_count": "票数",
        "rt": "计费吨",
        "teu": "TEU",
        "air_weight": "空运重量",
        "income": "收入",
        "cost": "成本",
        "profit": "利润",
        "profit_rate": "利润率(%)",
        "all_nominated_count": "指定货票数",
        "all_nominated_rt": "指定货RT",
        "all_nominated_profit": "指定货利润",
        "nomi_agent_count": "指定货代理数量"
    },
    "entity_analysis": {
        "pro2_system_id": "分公司代码",
        "entity_type": "实体类型",
        "customer_name": "客户名称",
        "nomi_agent_name": "指定货代理名称",
        "pod_code": "目的港代码",
        "consol_line": "集拼航线",
        "year": "年份",
        "month": "月份",
        "customer_count": "客户数量",
        "bkbl_count": "票数",
        "rt": "计费吨",
        "teu": "TEU",
        "air_weight": "空运重量",
        "income": "收入",
        "cost": "成本",
        "profit": "利润",
        "profit_rate": "利润率(%)",
        "pod_count": "目的港数量",
        "consol_rt": "集拼RT",
        "consol_20_count": "20尺集拼量",
        "consol_40_count": "40尺集拼量",
        "all_nominated_count": "指定货票数",
        "all_nominated_rt": "指定货RT",
        "all_nominated_customer_count": "指定货客户数量",
        "all_nominated_teu": "指定货TEU",
        "all_nominated_air_weight": "指定货空运重量",
        "all_nominated_income": "指定货收入",
        "all_nominated_cost": "指定货成本",
        "all_nominated_profit": "指定货利润",
        "all_nominated_profit_rate": "指定货利润率(%)",
        "port_nominated_count": "港代指定货票数",
        "port_nominated_rt": "港代指定货RT"
    },
    "top_customers_analysis": {
        "customer_name": "客户名称",
        "year": "年份",
        "month": "月份",
        "pro2_system_id": "分公司代码",
        "entity_type": "实体类型",
        "pod_count": "目的港数量",
        "bkbl_count": "票数",
        "rt": "计费吨",
        "teu": "TEU",
        "air_weight": "空运重量",
        "income": "收入",
        "cost": "成本",
        "profit": "利润",
        "profit_rate": "利润率(%)"
    },
    "top_bl_customers_analysis": {
        "customer_name": "客户名称",
        "year": "年份",
        "month": "月份",
        "pro2_system_id": "分公司代码",
        "entity_type": "实体类型",
        "pod_count": "目的港数量",
        "business_type_count": "业务类型数量",
        "bkbl_count": "票数",
        "rt": "计费吨",
        "teu": "TEU",
        "air_weight": "空运重量",
        "income": "收入",
        "cost": "成本",
        "profit": "利润",
        "profit_rate": "利润率(%)"
    },
    "consol_line_analysis": {
        "consol_line": "集拼航线",
        "year": "年份",
        "month": "月份",
        "pro2_system_id": "分公司代码",
        "entity_type": "实体类型",
        "bkbl_count": "票数",
        "rt": "计费吨",
        "income": "收入",
        "cost": "成本",
        "profit": "利润",
        "profit_rate": "利润率(%)",
        "consol_rt": "集拼RT",
        "consol_20_count": "20尺集拼量",
        "consol_40_count": "40尺集拼量",
        "all_nominated_count": "指定货票数",
        "all_nominated_rt": "指定货RT",
        "port_nominated_count": "港代指定货票数",
        "port_nominated_rt": "港代指定货RT"
    },
    "nomi_agents_analysis": {
        "nomi_agent_name": "指定货代理名称",
        "year": "年份",
        "month": "月份",
        "pro2_system_id": "分公司代码",
        "entity_type": "实体类型",
        "customer_count": "客户数量",
        "bkbl_count": "票数",
        "rt": "计费吨",
        "teu": "TEU",
        "air_weight": "空运重量",
        "income": "收入",
        "cost": "成本",
        "profit": "利润",
        "profit_rate": "利润率(%)"
    },
    "top_rt_ports_analysis": {
        "pod_code": "目的港代码",
        "year": "年份",
        "month": "月份",
        "pro2_system_id": "分公司代码",
        "entity_type": "实体类型",
        "customer_count": "客户数量",
        "bkbl_count": "票数",
        "rt": "计费吨",
        "teu": "TEU",
        "air_weight": "空运重量",
        "income": "收入",
        "cost": "成本",
        "profit": "利润",
        "profit_rate": "利润率(%)",
        "all_nominated_customer_count": "指定货客户数量",
        "all_nominated_count": "指定货票数",
        "all_nominated_rt": "指定货RT",
        "all_nominated_teu": "指定货TEU",
        "all_nominated_air_weight": "指定货空运重量",
        "all_nominated_income": "指定货收入",
        "all_nominated_cost": "指定货成本",
        "all_nominated_profit": "指定货利润",
        "all_nominated_profit_rate": "指定货利润率(%)"
    }
}

def convert_dataframe_to_records(df: pd.DataFrame, data_type: str) -> List[Dict]:
    """
    将DataFrame转换为记录格式，应用中文列名映射
    
    Args:
        df: pandas DataFrame
        data_type: 数据类型
        
    Returns:
        List[Dict]: 转换后的记录列表
    """
    if df.empty:
        return []
    
    # 获取列映射
    column_mapping = ANALYSIS_COLUMN_MAPPINGS.get(data_type, {})
    
    # 将DataFrame转换为记录列表
    records = df.to_dict('records')
    
    # 如果有列映射，应用映射
    if column_mapping:
        mapped_records = []
        for record in records:
            mapped_record = {}
            for eng_col, value in record.items():
                chinese_col = column_mapping.get(eng_col, eng_col)
                mapped_record[chinese_col] = value
            mapped_records.append(mapped_record)
        return mapped_records
    
    return records

async def export_analysis_result(result: Dict[str, Any], export_format: str = "both") -> Dict[str, str]:
    """
    导出分析结果
    
    Args:
        result: 分析函数的统一返回格式
        export_format: 导出格式 ("excel", "csv", "both")
        
    Returns:
        Dict[str, str]: 包含下载链接的字典
    """
    try:
        # 检查结果格式
        if not isinstance(result, dict) or 'pd_data' not in result or 'metadata' not in result:
            raise ValueError("结果格式不正确，缺少pd_data或metadata字段")
        
        df = result['pd_data']
        metadata = result['metadata']
        data_type_key = metadata.get('data_type', 'unknown')
        
        print(f"开始导出分析结果，数据类型: {data_type_key}, DataFrame形状: {df.shape}")
        
        # 将DataFrame转换为记录格式
        records = convert_dataframe_to_records(df, data_type_key)
        
        if not records:
            records = [{"提示": "暂无数据"}]
        
        # 构建导出数据结构
        export_data = {
            'data': records,
            'total_count': len(records)
        }
        
        # 确定导出的数据类型（用于optimized_export的兼容性）
        # 重要修复：所有分析和提取函数都使用"analysis"类型，避免字段映射问题
        if 'extract' in data_type_key or 'analysis' in data_type_key:
            export_data_type = "analysis"  # 使用analysis类型，避免字段名映射问题
        elif 'job' in data_type_key and 'extract' not in data_type_key:
            export_data_type = "job"  # 只有纯job分析才使用job类型
        else:
            export_data_type = "analysis"  # 默认使用analysis类型
        
        # 生成用户友好的文件名
        friendly_filename = generate_friendly_filename(data_type_key, metadata)
        
        # 导出文件
        if export_format == "both":
            return await export_both_formats(export_data, friendly_filename, export_data_type)
        elif export_format == "excel":
            excel_url = await export_to_oss(export_data, friendly_filename, export_data_type, "excel")
            return {"excel_url": excel_url}
        elif export_format == "csv":
            csv_url = await export_to_oss(export_data, friendly_filename, export_data_type, "csv")
            return {"csv_url": csv_url}
        else:
            raise ValueError(f"不支持的导出格式: {export_format}")
            
    except Exception as e:
        print(f"导出分析结果失败: {e}")
        raise

async def demo_export_analysis_functions():
    """
    演示导出各种分析函数的结果
    """
    print("=== 开始演示分析函数导出 ===")
    
    try:
        # 1. 测试Booking月度分析
        print("\n1. 导出Booking月度分析...")
        booking_monthly = await analysis_booking_by_month_data('2025-01-01', 2, 86021)
        booking_urls = await export_analysis_result(
            booking_monthly, 
            "both"
        )
        print(f"Booking月度分析导出完成:")
        for format_type, url in booking_urls.items():
            print(f"  {format_type}: {url}")
        
        # 2. 测试Booking数据提取
        print("\n2. 导出Booking数据提取...")
        booking_extract = await get_sea_air_profit_from_tokens_table_cached('2025-01-01', '2025-01-31', 86021)
        extract_urls = await export_analysis_result(
            booking_extract,
            "both"
        )
        print(f"Booking数据提取导出完成:")
        for format_type, url in extract_urls.items():
            print(f"  {format_type}: {url}")
        
        # 3. 测试指定货代理分析
        print("\n3. 导出指定货代理分析...")
        nomi_agents = await analysis_booking_nomi_agents('2025-01-01', 2, 5, 86021)
        agents_urls = await export_analysis_result(
            nomi_agents,
            "both"
        )
        print(f"指定货代理分析导出完成:")
        for format_type, url in agents_urls.items():
            print(f"  {format_type}: {url}")
        
        # 4. 测试业务票数前n大客户分析
        print("\n4. 导出业务票数前n大客户分析...")
        top_bl_customers = await analysis_booking_by_top_n_bl_customers('2025-01-01', 2, 5, 86021)
        bl_customers_urls = await export_analysis_result(
            top_bl_customers,
            "both"
        )
        print(f"业务票数前n大客户分析导出完成:")
        for format_type, url in bl_customers_urls.items():
            print(f"  {format_type}: {url}")
            
        print("\n=== 所有导出任务完成 ===")
        
        # 汇总所有链接
        print("\n=== 导出链接汇总 ===")
        print("Booking月度分析:")
        for format_type, url in booking_urls.items():
            print(f"  {format_type}: {url}")
        print("\nBooking数据提取:")
        for format_type, url in extract_urls.items():
            print(f"  {format_type}: {url}")
        print("\n指定货代理分析:")
        for format_type, url in agents_urls.items():
            print(f"  {format_type}: {url}")
        print("\n业务票数前n大客户分析:")
        for format_type, url in bl_customers_urls.items():
            print(f"  {format_type}: {url}")
            
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(demo_export_analysis_functions()) 