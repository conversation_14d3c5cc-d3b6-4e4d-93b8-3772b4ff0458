# 分析函数导出工具使用指南

## 概述

`enhanced_analysis_export.py` 是统一的分析函数导出工具，支持将所有分析和提取函数的结果导出为Excel和CSV格式。

## 文件位置

```
utils/basic/enhanced_analysis_export.py
```

## 主要功能

### 1. 统一导出接口

```python
from utils.basic.enhanced_analysis_export import export_analysis_result

# 导出单个格式
result = await export_analysis_result(
    analysis_result,  # 分析函数返回的统一格式结果
    "excel"  # 导出格式: "excel", "csv", "both"
)

# 导出双格式
result = await export_analysis_result(
    analysis_result,
    "filename_prefix", 
    "both"
)
```

### 2. 支持的函数

**分析类函数**：
- `analysis_booking_by_month_data` - Booking月度分析
- `analysis_job_by_month_data` - Job月度分析
- `analysis_booking_by_customer` - 客户分析
- `analysis_booking_by_top_n_customers` - 前N大客户分析
- `analysis_job_consol_line` - 集拼航线分析
- `analysis_booking_nomi_agents` - 指定货代理分析
- `analysis_booking_top_n_rt` - 前N大目的港RT分析

**提取类函数**：
- `get_sea_air_profit_from_tokens_table_cached` - 海空损益数据提取
- `get_job_details_from_tokens_table_cached` - 作业明细数据提取

## 使用示例

### 完整示例

```python
import asyncio
from utils.basic.enhanced_analysis_export import export_analysis_result
from utils.database.db_mysql_analysis import get_sea_air_profit_from_tokens_table_cached

async def main():
    # 1. 调用分析函数
    result = await get_sea_air_profit_from_tokens_table_cached(
        begin_date='2025-01-01',
        end_date='2025-01-31',
        pro2_system_id=86021
    )
    
    # 2. 导出为Excel和CSV
    export_urls = await export_analysis_result(
        result,
        "both"
    )
    
    # 3. 获取下载链接
    print(f"Excel: {export_urls['excel_url']}")
    print(f"CSV: {export_urls['csv_url']}")

if __name__ == "__main__":
    asyncio.run(main())
```

### 批量导出示例

```python
import asyncio
from utils.basic.enhanced_analysis_export import export_analysis_result
from utils.database.db_mysql_analysis import (
    analysis_booking_by_month_data,
    analysis_job_by_month_data,
    get_sea_air_profit_from_tokens_table_cached
)

async def batch_export():
    """批量导出多个分析结果"""
    
    # 分析函数配置
    analyses = [
        {
            'name': 'booking_monthly',
            'func': analysis_booking_by_month_data('2025-01-01', 2, 86021),
            'title': 'Booking月度分析'
        },
        {
            'name': 'job_monthly', 
            'func': analysis_job_by_month_data('2025-01-01', 2, 86021),
            'title': 'Job月度分析'
        },
        {
            'name': 'booking_extract',
            'func': get_sea_air_profit_from_tokens_table_cached('2025-01-01', '2025-01-31', 86021),
            'title': 'Booking数据提取'
        }
    ]
    
    # 批量导出
    for analysis in analyses:
        print(f"正在导出: {analysis['title']}")
        result = await analysis['func']
        export_urls = await export_analysis_result(
            result,
            "both"
        )
        print(f"  Excel: {export_urls['excel_url']}")
        print(f"  CSV: {export_urls['csv_url']}")
        print()

if __name__ == "__main__":
    asyncio.run(batch_export())
```

## 特性说明

### 1. 统一返回格式

所有分析函数返回统一格式：

```python
{
    "json_data": 原始JSON数据,
    "pd_data": pandas.DataFrame格式数据,
    "metadata": {
        "data_type": "数据类型标识",
        "columns_info": "列说明字典"
    }
}
```

### 2. 自动列排序

- DataFrame严格按照 `columns_info` 中的key顺序排列
- 只包含 `columns_info` 中定义的字段

### 3. 中文列名

- 导出的Excel/CSV文件使用中文列名
- 列名映射在 `ANALYSIS_COLUMN_MAPPINGS` 中定义

### 4. 86021特例处理

对于分公司86021的指定货数据：
- 当 `is_freehand = 0` 时，`salesman_name` 和 `salesman_department` 会被清空
- 指定货代理名称在 `nomi_agent_name` 字段中体现

### 5. 自动上传OSS

- 文件自动上传到阿里云OSS
- 返回短链接便于分享
- 支持Excel (.xlsx) 和CSV (.csv) 双格式

## 注意事项

1. **导入路径**：确保使用正确的导入路径 `utils.basic.enhanced_analysis_export`
2. **异步调用**：所有函数都是异步的，需要使用 `await` 关键字
3. **网络依赖**：导出功能需要网络连接以上传到OSS
4. **数据量**：大数据量导出可能需要较长时间

## 错误处理

```python
try:
    result = await analysis_function()
    export_urls = await export_analysis_result(result, "both")
except Exception as e:
    print(f"导出失败: {e}")
```

## 版本信息

- 当前版本：已全面优化
- 支持的数据类型：9种分析和提取函数
- 列映射：完全匹配，无缺失字段
- 特例处理：86021分公司指定货逻辑 