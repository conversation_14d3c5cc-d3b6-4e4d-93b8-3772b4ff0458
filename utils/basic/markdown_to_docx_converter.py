#!/usr/bin/env python3
"""
Markdown到DOCX转换工具
支持保留格式的文档转换
"""

import os
import re
from pathlib import Path
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
from datetime import datetime
from typing import List, Dict, Any
import asyncio

def setup_document_styles(doc: Document):
    """设置文档样式"""
    # 设置正文样式
    styles = doc.styles
    
    # 标题1样式
    if 'Heading 1' in styles:
        heading1 = styles['Heading 1']
        heading1.font.name = '黑体'
        heading1.font.size = Pt(16)
        heading1.font.bold = True
    
    # 标题2样式
    if 'Heading 2' in styles:
        heading2 = styles['Heading 2']
        heading2.font.name = '黑体'
        heading2.font.size = Pt(14)
        heading2.font.bold = True
    
    # 标题3样式
    if 'Heading 3' in styles:
        heading3 = styles['Heading 3']
        heading3.font.name = '黑体'
        heading3.font.size = Pt(12)
        heading3.font.bold = True
    
    # 正文样式
    if 'Normal' in styles:
        normal = styles['Normal']
        normal.font.name = '宋体'
        normal.font.size = Pt(12)
        normal.paragraph_format.line_spacing = 1.5

def parse_markdown_content(content: str) -> List[Dict[str, Any]]:
    """解析Markdown内容"""
    lines = content.split('\n')
    parsed_content = []
    
    for line in lines:
        line = line.strip()
        
        if not line:
            parsed_content.append({'type': 'empty', 'content': ''})
            continue
        
        # 标题处理
        if line.startswith('# '):
            parsed_content.append({'type': 'heading1', 'content': line[2:].strip()})
        elif line.startswith('## '):
            parsed_content.append({'type': 'heading2', 'content': line[3:].strip()})
        elif line.startswith('### '):
            parsed_content.append({'type': 'heading3', 'content': line[4:].strip()})
        elif line.startswith('#### '):
            parsed_content.append({'type': 'heading4', 'content': line[5:].strip()})
        
        # 粗体文本处理
        elif '**' in line:
            parsed_content.append({'type': 'bold', 'content': line})
        
        # 普通段落
        else:
            parsed_content.append({'type': 'paragraph', 'content': line})
    
    return parsed_content

def add_content_to_document(doc: Document, parsed_content: List[Dict[str, Any]]):
    """将解析的内容添加到文档"""
    for item in parsed_content:
        content_type = item['type']
        content = item['content']
        
        if content_type == 'empty':
            doc.add_paragraph()
        
        elif content_type == 'heading1':
            heading = doc.add_heading(content, level=1)
            heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        elif content_type == 'heading2':
            doc.add_heading(content, level=2)
        
        elif content_type == 'heading3':
            doc.add_heading(content, level=3)
        
        elif content_type == 'heading4':
            doc.add_heading(content, level=4)
        
        elif content_type == 'bold':
            # 处理粗体文本
            paragraph = doc.add_paragraph()
            parts = content.split('**')
            for i, part in enumerate(parts):
                if i % 2 == 0:
                    paragraph.add_run(part)
                else:
                    run = paragraph.add_run(part)
                    run.bold = True
        
        elif content_type == 'paragraph':
            # 处理特殊格式的段落
            if content.startswith('　　'):
                # 中文段落缩进
                paragraph = doc.add_paragraph(content)
                paragraph.paragraph_format.first_line_indent = Inches(0.5)
            else:
                doc.add_paragraph(content)

async def convert_markdown_to_docx(markdown_file_path: str, output_dir: str = None) -> str:
    """
    将Markdown文件转换为DOCX格式
    
    Args:
        markdown_file_path: Markdown文件路径
        output_dir: 输出目录，默认为输入文件同目录
        
    Returns:
        str: 生成的DOCX文件路径
    """
    try:
        # 检查输入文件
        if not os.path.exists(markdown_file_path):
            raise FileNotFoundError(f"Markdown文件不存在: {markdown_file_path}")
        
        # 读取Markdown内容
        with open(markdown_file_path, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
        
        # 创建Word文档
        doc = Document()
        
        # 设置文档样式
        setup_document_styles(doc)
        
        # 解析Markdown内容
        parsed_content = parse_markdown_content(markdown_content)
        
        # 添加内容到文档
        add_content_to_document(doc, parsed_content)
        
        # 确定输出路径
        input_path = Path(markdown_file_path)
        if output_dir:
            output_path = Path(output_dir) / f"{input_path.stem}.docx"
        else:
            output_path = input_path.parent / f"{input_path.stem}.docx"
        
        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存文档
        doc.save(str(output_path))
        
        print(f"✅ 转换完成: {output_path}")
        return str(output_path)
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        raise

async def main():
    """主函数"""
    markdown_file = "/Users/<USER>/gitee-code-files/mcp-cms/logs/byd_financial_analysis_paper-v7.md"
    
    print("开始转换Markdown到DOCX...")
    docx_file = await convert_markdown_to_docx(markdown_file)
    print(f"DOCX文件已生成: {docx_file}")

if __name__ == "__main__":
    asyncio.run(main())