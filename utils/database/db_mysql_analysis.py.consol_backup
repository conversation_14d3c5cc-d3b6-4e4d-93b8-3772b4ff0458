# 业务数据的分析, 包括对特例情况的修正
"""
- 通过对MySQL数据库表t_job_details和表t_booking_details的分析, 获取到业务分析数据 --> 特例: 对于pro2_system_id的值为86021的业务, Job表中的如下字段数据, 需要通过对Booking表(特例修正后的)的汇总数据进行分析获得:
    1. 'all_nominated_count',      # 全部指定货票数 --> 特例修正后的is_free_hand字段为0的booking的'business_no'数量(count)
    2. 'all_nominated_rt',         # 全部指定货RT --> 特例修正后的is_free_hand字段为0的booking的'lcl_rt'合计值(sum)
- 参考脚本 utils/basic/profit_data_scheduler.py 中对于t_job_details和t_booking_details表的定义和中文名称含义映射
- Job & Booking 表的pro2_system_id的数值, 映射了不同给的分公司的业务数据: 86532-青岛(港口代码QDO), 86021-上海(港口代码SHA), 852-香港(港口代码HKG), 8103-东京(港口代码TKY)

对于Booking业务, 需要分析的基础数据包括: 
    'business_type_name',    # 业务类型
    'job_date',             # 工作档日期
    'job_file_no',          # 工作档编号
    'business_no',          # 订舱提单编号
    'shipper_name',         # 客户名称
    'vessel',               # 船名
    'voyage',               # 航次 
    'pol_code',             # 提单起运地 --> 如果提单起运地为空, 则使用sailing_pol的值
    'pod_code',             # 提单卸货地 --> 如果提单卸货地为空, 则使用pro2_system_id的值对应的港口代码(特例:pro2_system_id的值为86532除外, 因为86532的卸货港需要通过'操作部门'判断)
    'service_mode',         # 服务模式
    'lcl_rt',               # 拼箱RT
    'teu',                  # TEU
    'air_weight',           # 空运重量
    'income',               # 收入
    'cost',                 # 成本
    'profit',               # 利润
    'is_transhipment',      # 是否转运
    'transhipment_profit',  # 转运利润
    'is_free_hand',         # 自揽货 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'salesman_name',        # 业务员 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'salesman_dept_name',   # 营业员部门 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'nomi_agent_name',      # 指定货代理 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'operator_name',        # 操作员
    'operator_dept_name',   # 操作部门
    'coloader_name',        # Coloader名称
    'job_handling_agent_name'  # 工作档代理

对于Job业务, 需要分析的基础数据包括: 
    'business_type_name',  # 业务类型
    'job_date',           # 工作档日期
    'job_file_no',        # 工作档编号
    'vessel',             # 船名
    'voyage',             # 航次
    'pol_code',           # 起运港
    'pod_code',           # 卸货港 --> 如果卸货港为空, 则使用pro2_system_id的值对应的港口代码(特例:pro2_system_id的值为86532除外, 因为86532的卸货港需要通过'操作部门'判断)
    'bk_count',           # 订舱数
    'bl_count',           # 提单数
    'total_rt',           # 计费吨
    'total_teu',          # TEU
    'income',             # 收入
    'cost',               # 成本
    'profit',             # 利润
    'transhipment_count', # 转运票数
    'transhipment_profit', # 转运利润
    'operator_name',      # 操作员
    'job_handling_agent_name',  # 工作档代理
    'all_nominated_count',      # 全部指定货票数
    'all_nominated_rt',         # 全部指定货RT
    'port_agent_nominated_count', # 港代指定货票数
    'port_agent_nominated_rt',    # 港代指定货RT
    'is_consol',               # 是否集拼
    'consol_20_count',        # 20集拼量
    'consol_40_count',        # 40集拼量
    'operator_dept_name',     # 操作部门
    'is_op_finished',         # 操作完成
    'is_checked'              # 审核状态

** 对于特例情况的值的判断规则: **
对于pro2_system_id的值为86532的业务, pod_code(卸货港)的值为空时, 需要通过'操作部门'判断对应的卸货港的规则:
    1. 如果'操作部门'以'QD/'开头, 则pod_code的值视为'QDO'
    2. 如果'操作部门'以'GZ/'开头, 则pod_code的值视为'GZG'
    3. 如果'操作部门'以'NQ/'开头, 则pod_code的值视为'NBO'
    4. 如果'操作部门'以'XM/'开头, 则pod_code的值视为'XMN'

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'is_free_hand'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'is_free_hand'字段的值视为0
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'is_free_hand'字段的值视为0
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'is_free_hand'字段的值视为1

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'salesman_name'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'salesman_name'字段的值视为不变
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'salesman_name'字段的值视为空值
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'salesman_name'字段的值视为不变

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'salesman_dept_name'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'salesman_dept_name'字段的值视为不变
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'salesman_dept_name'字段的值视为空值
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'salesman_dept_name'字段的值视为不变

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'nomi_agent_name'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'nomi_agent_name'字段的值视为不变
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'nomi_agent_name'字段的视为值:
            2.1.1 如果'salesman_name'字段的值为包含'指定货'或者'指定'字样, 则'nomi_agent_name'字段的值视为'salesman_name'字段的值删除'指定货'或者'指定'字样后的值
            2.1.2 如果'salesman_name'字段的值为不包含'指定货'或者'指定'字样, 则'nomi_agent_name'字段的值视为'salesman_name'字段的值
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'nomi_agent_name'字段的值视为空值

"""
# 对常用的分析场景, 使用固定函数进行分析
# 对于常用场景之外的分析场景, 使用动态函数进行分析: 给出示例sql, 由AI根据用户需求进行实际SQL的构建（暂时不需要实现）

import asyncio
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from typing import List, Dict, Any, Optional
import sys
import os
import aiomysql
import traceback
from collections import defaultdict

# 生产环境优化的路径处理
def _setup_project_path():
    """
    设置项目根目录到Python路径中
    适用于开发环境和生产环境（Ubuntu服务器）
    """
    try:
        # 获取当前文件的绝对路径
        current_file = os.path.abspath(__file__)
        
        # 计算项目根目录（向上两级目录）
        project_root = os.path.dirname(os.path.dirname(current_file))
        
        # 验证项目根目录是否存在utils目录
        if os.path.exists(os.path.join(project_root, 'utils')):
            # 只有在路径不存在时才添加，避免重复
            if project_root not in sys.path:
                sys.path.insert(0, project_root)  # 使用insert(0)确保优先级
        else:
            # 如果找不到utils目录，使用环境变量或当前工作目录
            fallback_root = os.getenv('MCP_CMS_ROOT', os.getcwd())
            if fallback_root not in sys.path:
                sys.path.insert(0, fallback_root)
    except Exception as e:
        # 生产环境的错误处理
        print(f"Warning: Failed to setup project path: {e}")
        # 使用当前工作目录作为后备方案
        if os.getcwd() not in sys.path:
            sys.path.insert(0, os.getcwd())

# 执行路径设置
_setup_project_path()

from utils.basic.data_conn_unified import get_mysql_connection, MYSQL_DB_MCP
from utils.basic.data_cache_manager import async_cached_data_function

# 分析工具函数: 应用特例处理逻辑
def apply_special_case_processing(record: Dict[str, Any]) -> Dict[str, Any]:
    """
    应用特例处理逻辑
    
    Args:
        record: 数据记录
        
    Returns:
        处理后的数据记录
    """
    processed_record = record.copy()
    pro2_system_id = record.get('pro2_system_id')
    
    # 处理 pro2_system_id = 86532 的特例
    if pro2_system_id == 86532:
        # 处理 pod_code 为空的情况
        if not record.get('bill_pod') or record.get('bill_pod') == '':
            operator_dept = record.get('operator_department', '')
            if operator_dept.startswith('QD/'):
                processed_record['bill_pod'] = 'QDO'
            elif operator_dept.startswith('GZ/'):
                processed_record['bill_pod'] = 'GZG'
            elif operator_dept.startswith('NQ/'):
                processed_record['bill_pod'] = 'NBO'
            elif operator_dept.startswith('XM/'):
                processed_record['bill_pod'] = 'XMN'
    
    # 处理 pro2_system_id = 86021 的特例
    elif pro2_system_id == 86021:
        original_is_freehand = record.get('is_freehand', 0)
        salesman_dept_name = record.get('salesman_department', '')
        salesman_name = record.get('salesman_name', '')
        
        # 处理 is_freehand 字段
        if original_is_freehand == 0:
            processed_record['is_freehand'] = 0
        elif original_is_freehand == 1:
            if salesman_dept_name == '指定货业务':
                processed_record['is_freehand'] = 0
            else:
                processed_record['is_freehand'] = 1
        
        # 处理 salesman_name 字段
        if processed_record['is_freehand'] == 0:
            # 保持不变
            pass
        elif processed_record['is_freehand'] == 1:
            if salesman_dept_name == '指定货业务':
                processed_record['salesman_name'] = None
            # 否则保持不变
        
        # 处理 salesman_dept_name 字段
        if processed_record['is_freehand'] == 0:
            # 保持不变
            pass
        elif processed_record['is_freehand'] == 1:
            if salesman_dept_name == '指定货业务':
                processed_record['salesman_department'] = None
            # 否则保持不变
        
        # 处理 nomi_agent_name 字段（为记录添加这个字段）
        # 对于指定货（is_freehand=0），需要正确设置nomi_agent_name
        if processed_record['is_freehand'] == 0:
            # 如果原始记录来自"指定货业务"部门，从salesman_name中提取代理名称
            if salesman_dept_name == '指定货业务':
                if salesman_name:
                    if '指定货' in salesman_name:
                        processed_record['nomi_agent_name'] = salesman_name.replace('指定货', '').strip()
                    elif '指定' in salesman_name:
                        processed_record['nomi_agent_name'] = salesman_name.replace('指定', '').strip()
                    else:
                        processed_record['nomi_agent_name'] = salesman_name.strip()
                else:
                    processed_record['nomi_agent_name'] = ''
            # 否则保持使用原有的job_handling_agent
            # （这个会在后面的通用逻辑中处理）
        else:
            # 非指定货，清空nomi_agent_name
            processed_record['nomi_agent_name'] = ''
    
    # 为所有记录添加 nomi_agent_name 字段（如果不存在）
    if 'nomi_agent_name' not in processed_record:
        # 修正：使用job_handling_agent作为指定货代理字段，因为bl_handling_agent字段基本为空
        processed_record['nomi_agent_name'] = record.get('job_handling_agent', '')
    
    return processed_record

# 分析工具函数: 获取业务类型映射
def get_business_type_mapping() -> Dict[str, str]:
    """
    获取业务类型映射
    
    Returns:
        业务类型映射字典
    """
    return {
        '海运出口': 'sea_export',
        '海运进口': 'sea_import', 
        '海运三角贸易': 'triangle_trade',  # 修正：数据库中是"海运三角贸易"
        '空运': 'air'  # 修正：数据库中是"空运"而不是分出口和进口
    }

# 分析工具函数: 计算汇总数据
def calculate_aggregated_data(records: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    计算汇总数据
    
    Args:
        records: 数据记录列表
        
    Returns:
        汇总数据字典
    """
    if not records:
        return {
            "customer_count": 0,
            "bkbl_count": 0,
            "rt": 0,
            "teu": 0,
            "air_weight": 0,
            "income": 0,
            "cost": 0,
            "profit": 0,
            "profit_rate": 0,
            "all_nominated_count": 0,
            "all_nominated_rt": 0,
            "all_nominated_profit": 0,
            "nomi_agent_count": 0,
        }
    
    # 客户数量（去重）
    customers = set()
    for record in records:
        if record.get('client_name'):
            customers.add(record['client_name'])
    
    # 指定货代理数量（去重）
    nomi_agents = set()
    nominated_records = []
    for record in records:
        if record.get('is_freehand') == 0:  # 指定货
            nominated_records.append(record)
            # 修正：正确过滤空字符串和None值
            nomi_agent_name = record.get('nomi_agent_name')
            if nomi_agent_name and nomi_agent_name.strip():
                nomi_agents.add(nomi_agent_name.strip())
    
    # 基础汇总
    total_income = sum(float(record.get('income', 0) or 0) for record in records)
    total_cost = abs(sum(float(record.get('cost', 0) or 0) for record in records))
    total_profit = sum(float(record.get('profit', 0) or 0) for record in records)
    
    # 指定货汇总
    nominated_rt = sum(float(record.get('lcl_rt', 0) or 0) for record in nominated_records)
    nominated_profit = sum(float(record.get('profit', 0) or 0) for record in nominated_records)
    
    # 计算利润率
    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
    
    return {
        "customer_count": len(customers),
        "bkbl_count": len(records),
        "rt": sum(float(record.get('lcl_rt', 0) or 0) for record in records),
        "teu": sum(float(record.get('teu', 0) or 0) for record in records),
        "air_weight": sum(float(record.get('air_weight', 0) or 0) for record in records),
        "income": total_income,
        "cost": total_cost,
        "profit": total_profit,
        "profit_rate": round(profit_rate, 2),
        "all_nominated_count": len(nominated_records),
        "all_nominated_rt": nominated_rt,
        "all_nominated_profit": nominated_profit,
        "nomi_agent_count": len(nomi_agents),
    }

# 分析工具函数: 计算Job数据汇总
def calculate_job_aggregated_data(records: List[Dict[str, Any]], business_type: str = '') -> Dict[str, Any]:
    """
    计算Job数据汇总
    
    Args:
        records: Job数据记录列表
        business_type: 业务类型，用于确定票数计算方式
        
    Returns:
        汇总数据字典
    """
    if not records:
        return {
            "bkbl_count": 0,
            "rt": 0,
            "teu": 0,
            "air_weight": 0,
            "income": 0,
            "cost": 0,
            "profit": 0,
            "profit_rate": 0,
            "all_nominated_count": 0,
            "all_nominated_rt": 0,
            "all_nominated_teu": 0,
            "consol_count": 0,
            "consol_rt": 0,
            "consol_20_count": 0,
            "consol_40_count": 0,
        }
    
    # 基础汇总
    total_income = sum(float(record.get('income', 0) or 0) for record in records)
    total_cost = abs(sum(float(record.get('cost', 0) or 0) for record in records))
    total_profit = sum(float(record.get('profit', 0) or 0) for record in records)
    
    # 票数计算：海运出口用bk_count，其他用bill_count
    if business_type == '海运出口':
        total_count = sum(int(record.get('bk_count', 0) or 0) for record in records)
    else:
        total_count = sum(int(record.get('bill_count', 0) or 0) for record in records)
    
    # 集拼航线数量计算：对于is_consolidation=1的记录，统计不同pol_code/pod_code组合的数量
    consol_lines = set()
    consol_records = []
    for record in records:
        if record.get('is_consolidation') == 1:
            consol_records.append(record)
            pol_code = record.get('pol_code', '')
            pod_code = record.get('pod_code', '')
            if pol_code and pod_code:
                consol_lines.add(f"{pol_code}/{pod_code}")
    
    # 集拼数据汇总
    consol_rt = sum(float(record.get('rt', 0) or 0) for record in consol_records)
    consol_20_count = sum(int(record.get('consolidation_20', 0) or 0) for record in consol_records)
    consol_40_count = sum(int(record.get('consolidation_40', 0) or 0) for record in consol_records)
    
    # 计算利润率
    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
    
    return {
        "bkbl_count": total_count,
        "rt": sum(float(record.get('rt', 0) or 0) for record in records),
        "teu": sum(float(record.get('teu', 0) or 0) for record in records),
        "air_weight": 0,  # Job表中没有空运重量字段
        "income": total_income,
        "cost": total_cost,
        "profit": total_profit,
        "profit_rate": round(profit_rate, 2),
        "all_nominated_count": sum(int(record.get('nomi_count', 0) or 0) for record in records),
        "all_nominated_rt": sum(float(record.get('nomi_rt', 0) or 0) for record in records),
        "all_nominated_teu": 0,  # 需要从Booking表计算
        "consol_count": len(consol_lines),
        "consol_rt": consol_rt,
        "consol_20_count": consol_20_count,
        "consol_40_count": consol_40_count,
    }

# 分析工具函数: 应用Job数据的特例处理逻辑
def apply_special_case_processing_job(record: Dict[str, Any]) -> Dict[str, Any]:
    """
    应用Job数据的特例处理逻辑
    
    Args:
        record: Job数据记录
        
    Returns:
        处理后的数据记录
    """
    processed_record = record.copy()
    pro2_system_id = record.get('pro2_system_id')
    
    # 处理 pro2_system_id = 86532 的特例
    if pro2_system_id == 86532:
        # 处理 pod_code 为空的情况
        if not record.get('pod_code') or record.get('pod_code') == '':
            operator_dept = record.get('operator_department', '')
            if operator_dept.startswith('QD/'):
                processed_record['pod_code'] = 'QDO'
            elif operator_dept.startswith('GZ/'):
                processed_record['pod_code'] = 'GZG'
            elif operator_dept.startswith('NQ/'):
                processed_record['pod_code'] = 'NBO'
            elif operator_dept.startswith('XM/'):
                processed_record['pod_code'] = 'XMN'
    
    return processed_record

# 分析工具函数: 从Booking表获取Job的指定货数据
async def get_job_nominated_data_from_booking(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    从Booking表获取Job的指定货数据（用于86021特例）
    注意：对于86021，需要应用特例处理来正确判断is_freehand字段
    
    Args:
        begin_date: 开始日期
        end_date: 结束日期
        pro2_system_id: 分公司代码
        
    Returns:
        指定货数据汇总
    """
    # 如果是86021，需要获取更多字段来进行特例处理
    if pro2_system_id == 86021:
        sql = """
            SELECT 
                job_no,
                is_freehand,
                salesman_department,
                lcl_rt,
                teu
            FROM t_booking_details t1
            WHERE t1.job_date >= %s AND t1.job_date <= %s
            AND t1.pro2_system_id = %s
            AND t1.id = (
                SELECT MAX(t2.id) 
                FROM t_booking_details t2 
                WHERE t2.job_id = t1.job_id 
                AND t2.bkbl_no = t1.bkbl_no
                AND t2.pro2_system_id = t1.pro2_system_id
            )
        """
        params = [begin_date, end_date, pro2_system_id]
    else:
        sql = """
            SELECT 
                t1.job_no,
                COUNT(*) as nominated_count,
                SUM(t1.lcl_rt) as nominated_rt,
                SUM(t1.teu) as nominated_teu
            FROM t_booking_details t1
            WHERE t1.job_date >= %s AND t1.job_date <= %s
            AND t1.is_freehand = 0
            AND t1.id = (
                SELECT MAX(t2.id) 
                FROM t_booking_details t2 
                WHERE t2.job_id = t1.job_id 
                AND t2.bkbl_no = t1.bkbl_no
                AND t2.pro2_system_id = t1.pro2_system_id
            )
        """
        params = [begin_date, end_date]
        
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += " GROUP BY t1.job_no"
    
    async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            await cursor.execute(sql, params)
            results = await cursor.fetchall()
    
    # 构建Job编号到指定货数据的映射
    job_nominated_map = {}
    
    if pro2_system_id == 86021:
        # 对86021应用特例处理
        job_data = {}
        for result in results:
            job_no = result['job_no']
            if job_no not in job_data:
                job_data[job_no] = []
            job_data[job_no].append(result)
        
        # 对每个Job应用特例处理并汇总
        for job_no, records in job_data.items():
            nominated_count = 0
            nominated_rt = 0
            nominated_teu = 0
            
            for record in records:
                # 应用特例处理
                processed_record = apply_special_case_processing(record)
                
                # 如果处理后是指定货（is_freehand=0），则计入统计
                if processed_record.get('is_freehand') == 0:
                    nominated_count += 1
                    nominated_rt += float(record.get('lcl_rt', 0) or 0)
                    nominated_teu += float(record.get('teu', 0) or 0)
            
            if nominated_count > 0:
                job_nominated_map[job_no] = {
                    'nominated_count': nominated_count,
                    'nominated_rt': nominated_rt,
                    'nominated_teu': nominated_teu
                }
    else:
        # 其他分公司直接使用查询结果
        for result in results:
            job_no = result['job_no']
            job_nominated_map[job_no] = {
                'nominated_count': result['nominated_count'] or 0,
                'nominated_rt': float(result['nominated_rt'] or 0),
                'nominated_teu': float(result['nominated_teu'] or 0)
            }
    
    return job_nominated_map


# 常用分析场景1: 分析连续n月每个月的数据(Booking分析），包括: 海运出口/海运进口/三角贸易/空运 的 客户数量/票数/RT/TEU/AirWeight/收入/成本/利润/利润率/指定货票数/指定货RT/指定货TEU/指定货利润/指定货代理数量
async def analysis_booking_on_month_by_month_data(begin_date: str, month_count: int, pro2_system_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    分析连续n月每个月的数据(Booking分析），包括: 海运出口/海运进口/三角贸易/空运 的 客户数量/票数/RT/TEU/AirWeight/收入/成本/利润/利润率/指定货票数/指定货RT/指定货TEU/指定货利润
    其中, 指定货代理数量: 指定货代理数量 = 不同的'nomi_agent_name'字段的值的数量(不重复)
    注意: pro2_system_id的值为86021时, 对于指定货的判断依据
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        pro2_system_id: 分公司代码, 可选值为: 86532-青岛(港口代码QDO), 86021-上海(港口代码SHA), 852-香港(港口代码HKG), 8103-东京(港口代码TKY), 如果为空, 则分析全部分公司
    
    Returns:
        List[Dict], 包含连续n月每个月的数据(Booking分析），包括: 海运出口/海运进口/三角贸易/空运 的各项指标
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        
        # 验证开始日期是否为每月1日
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        results = []
        business_type_mapping = get_business_type_mapping()
        
        # 按月循环分析
        for month_offset in range(month_count):
            current_month_start = start_date + relativedelta(months=month_offset)
            current_month_end = current_month_start + relativedelta(months=1) - timedelta(days=1)
            
            # 构建SQL查询
            base_sql = """
                SELECT 
                    t1.job_type_cn,
                    t1.job_date,
                    t1.job_no,
                    t1.bkbl_no,
                    t1.client_name,
                    t1.vessel,
                    t1.voyage,
                    t1.job_pol,
                    t1.bill_pol,
                    t1.bill_pod,
                    t1.service_mode,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_department,
                    t1.operator_name,
                    t1.operator_department,
                    t1.coloader_name,
                    t1.job_handling_agent,
                    t1.bl_handling_agent,
                    t1.is_transhipment,
                    t1.pro2_system_id
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.id = (
                    SELECT MAX(t2.id) 
                    FROM t_booking_details t2 
                    WHERE t2.job_id = t1.job_id 
                    AND t2.bkbl_no = t1.bkbl_no
                AND t2.pro2_system_id = t1.pro2_system_id
                )
            """
            
            params = [current_month_start.strftime('%Y-%m-%d'), current_month_end.strftime('%Y-%m-%d')]
            
            # 添加分公司过滤条件
            if pro2_system_id is not None:
                base_sql += " AND t1.pro2_system_id = %s"
                params.append(pro2_system_id)
            
            # 执行查询
            async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(base_sql, params)
                    raw_records = await cursor.fetchall()
            
            # 处理特例情况
            processed_records = []
            for record in raw_records:
                processed_record = apply_special_case_processing(record)
                processed_records.append(processed_record)
            
            # 按业务类型分组
            business_type_groups = {}
            for record in processed_records:
                business_type = record.get('job_type_cn', '')
                if business_type not in business_type_groups:
                    business_type_groups[business_type] = []
                business_type_groups[business_type].append(record)
            
            # 构建月度数据
            month_data = {
                "year": current_month_start.strftime('%Y'),
                "month": current_month_start.strftime('%m'),
                "pro2_system_id": pro2_system_id,
                "sea_export_data": [],
                "sea_import_data": [],
                "triangle_trade_data": [],
                "air_data": []
            }
            
            # 计算各业务类型的汇总数据
            for business_type, records in business_type_groups.items():
                aggregated_data = calculate_aggregated_data(records)
                
                if business_type == '海运出口':
                    month_data["sea_export_data"].append(aggregated_data)
                elif business_type == '海运进口':
                    month_data["sea_import_data"].append(aggregated_data)
                elif business_type == '海运三角贸易':  # 修正：数据库中是"海运三角贸易"
                    month_data["triangle_trade_data"].append(aggregated_data)
                elif business_type == '空运':  # 修正：数据库中是"空运"
                    # 空运数据不包括TEU
                    air_data = aggregated_data.copy()
                    air_data.pop('teu', None)
                    air_data.pop('all_nominated_rt', None)  # 空运不计算指定货RT
                    month_data["air_data"].append(air_data)
            
            # 如果某个业务类型没有数据，添加空数据
            for data_key in ["sea_export_data", "sea_import_data", "triangle_trade_data", "air_data"]:
                if not month_data[data_key]:
                    empty_data = calculate_aggregated_data([])
                    if data_key == "air_data":
                        empty_data.pop('teu', None)
                        empty_data.pop('all_nominated_rt', None)
                    month_data[data_key].append(empty_data)
            
            results.append(month_data)
        
        return results
        
    except Exception as e:
        print(f"分析数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

# 常用分析场景2: 分析连续n月每个月的数据(Job分析），包括: 海运出口/海运进口/三角贸易/空运 的 票数/RT/TEU/AirWeight/收入/成本/利润/利润率/指定货票数/指定货RT/指定货TEU/集拼航线数量/集拼RT/集拼20/集拼40
async def analysis_job_on_month_by_month_data(begin_date: str, month_count: int, pro2_system_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    分析连续n月每个月的数据(Job分析），包括: 海运出口/海运进口/三角贸易/空运 的 票数/RT/TEU/AirWeight/收入/成本/利润/利润率/指定货票数/指定货RT/指定货TEU/集拼航线数量/集拼RT/集拼20/集拼40
    其中, 集拼航线数量: 集拼航线数量 = 对于'is_consolidation'字段的值为1的'job_file_no'字段, 不同的'pol_code'/'pod_code'组合的数量(不重复)
    注意: pro2_system_id的值为86021时, 对于指定货的判断依据
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        pro2_system_id: 分公司代码, 可选值为: 86532-青岛(港口代码QDO), 86021-上海(港口代码SHA), 852-香港(港口代码HKG), 8103-东京(港口代码TKY), 如果为空, 则分析全部分公司
    
    Returns:
        List[Dict], 包含连续n月每个月的数据(Job分析）
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        
        # 验证开始日期是否为每月1日
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        results = []
        business_type_mapping = get_business_type_mapping()
        
        # 按月循环分析
        for month_offset in range(month_count):
            current_month_start = start_date + relativedelta(months=month_offset)
            current_month_end = current_month_start + relativedelta(months=1) - timedelta(days=1)
            
            # 构建SQL查询Job表
            base_sql = """
                SELECT 
                    t1.job_type_cn,
                    t1.job_date,
                    t1.job_no,
                    t1.vessel,
                    t1.voyage,
                    t1.pol_code,
                    t1.pod_code,
                    t1.bk_count,
                    t1.rt,
                    t1.teu,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.operator_name,
                    t1.job_handling_agent,
                    t1.nomi_count,
                    t1.nomi_rt,
                    t1.is_consolidation,
                    t1.bill_count,
                    t1.consolidation_20,
                    t1.consolidation_40,
                    t1.operator_department,
                    t1.is_op_finished,
                    t1.is_checked,
                    t1.pro2_system_id
                FROM t_job_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.id = (
                    SELECT MAX(t2.id) 
                    FROM t_job_details t2 
                    WHERE t2.job_id = t1.job_id
                    AND t2.pro2_system_id = t1.pro2_system_id
                )
            """
            
            params = [current_month_start.strftime('%Y-%m-%d'), current_month_end.strftime('%Y-%m-%d')]
            
            # 添加分公司过滤条件
            if pro2_system_id is not None:
                base_sql += " AND t1.pro2_system_id = %s"
                params.append(pro2_system_id)
            
            # 执行查询
            async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(base_sql, params)
                    raw_records = await cursor.fetchall()
            
            # 处理特例情况
            processed_records = []
            for record in raw_records:
                processed_record = apply_special_case_processing_job(record)
                processed_records.append(processed_record)
            
            # 对于86021（上海），需要从Booking表获取指定货数据
            job_nominated_map = {}
            if pro2_system_id == 86021 or pro2_system_id is None:
                job_nominated_map = await get_job_nominated_data_from_booking(
                    current_month_start.strftime('%Y-%m-%d'),
                    current_month_end.strftime('%Y-%m-%d'),
                    86021 if pro2_system_id == 86021 else None
                )
            
            # 按业务类型分组
            business_type_groups = {}
            for record in processed_records:
                business_type = record.get('job_type_cn', '')
                if business_type not in business_type_groups:
                    business_type_groups[business_type] = []
                
                # 如果是86021的数据，更新指定货信息
                if record.get('pro2_system_id') == 86021:
                    job_no = record.get('job_no', '')
                    if job_no in job_nominated_map:
                        nominated_data = job_nominated_map[job_no]
                        record['nomi_count'] = nominated_data['nominated_count']
                        record['nomi_rt'] = nominated_data['nominated_rt']
                        record['nomi_teu'] = nominated_data['nominated_teu']
                
                business_type_groups[business_type].append(record)
            
            # 构建月度数据
            month_data = {
                "year": current_month_start.strftime('%Y'),
                "month": current_month_start.strftime('%m'),
                "pro2_system_id": pro2_system_id,
                "sea_export_data": [],
                "sea_import_data": [],
                "triangle_trade_data": [],
                "air_data": []
            }
            
            # 计算各业务类型的汇总数据
            for business_type, records in business_type_groups.items():
                aggregated_data = calculate_job_aggregated_data(records, business_type)
                
                if business_type == '海运出口':
                    month_data["sea_export_data"].append(aggregated_data)
                elif business_type == '海运进口':
                    month_data["sea_import_data"].append(aggregated_data)
                elif business_type == '海运三角贸易':
                    month_data["triangle_trade_data"].append(aggregated_data)
                elif business_type == '空运':
                    # 空运数据不包括TEU和集拼相关数据
                    air_data = aggregated_data.copy()
                    air_data.pop('teu', None)
                    air_data.pop('all_nominated_rt', None)
                    air_data.pop('all_nominated_teu', None)
                    air_data.pop('consol_count', None)
                    air_data.pop('consol_rt', None)
                    air_data.pop('consol_20_count', None)
                    air_data.pop('consol_40_count', None)
                    month_data["air_data"].append(air_data)
            
            # 如果某个业务类型没有数据，添加空数据
            for data_key in ["sea_export_data", "sea_import_data", "triangle_trade_data", "air_data"]:
                if not month_data[data_key]:
                    # 根据数据类型确定业务类型名称
                    if data_key == "sea_export_data":
                        empty_business_type = "海运出口"
                    elif data_key == "sea_import_data":
                        empty_business_type = "海运进口"
                    elif data_key == "triangle_trade_data":
                        empty_business_type = "海运三角贸易"
                    else:
                        empty_business_type = "空运"
                    
                    empty_data = calculate_job_aggregated_data([], empty_business_type)
                    if data_key == "air_data":
                        empty_data.pop('teu', None)
                        empty_data.pop('all_nominated_rt', None)
                        empty_data.pop('all_nominated_teu', None)
                        empty_data.pop('consol_count', None)
                        empty_data.pop('consol_rt', None)
                        empty_data.pop('consol_20_count', None)
                        empty_data.pop('consol_40_count', None)
                    month_data[data_key].append(empty_data)
            
            results.append(month_data)
        
        return results
        
    except Exception as e:
        print(f"分析Job数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

# 常用场景3: 分析连续n月每个月某客户的数据(Booking分析）- 检查ok
async def optimized_analysis_booking_by_customer(begin_date: str, month_count: int, customer_name: str, pro2_system_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    高性能优化版本: 分析连续n月每个月指定客户的数据(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 在内存中进行月份和目的港分组，减少数据库负载
    4. 正确处理86021特例规则
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        customer_name: 客户名称
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含连续n月每个月指定客户的数据，按卸货港分组
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询客户 '{customer_name}': {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数优化的SQL查询
        sql = """
            SELECT 
                job_date,
                job_type_cn,
                job_no,
                bkbl_no,
                client_name,
                vessel,
                voyage,
                job_pol,
                bill_pol,
                bill_pod,
                service_mode,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                is_freehand,
                salesman_name,
                salesman_department,
                operator_name,
                operator_department,
                coloader_name,
                job_handling_agent,
                bl_handling_agent,
                is_transhipment,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_date,
                    t1.job_type_cn,
                    t1.job_no,
                    t1.bkbl_no,
                    t1.client_name,
                    t1.vessel,
                    t1.voyage,
                    t1.job_pol,
                    t1.bill_pol,
                    t1.bill_pod,
                    t1.service_mode,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_department,
                    t1.operator_name,
                    t1.operator_department,
                    t1.coloader_name,
                    t1.job_handling_agent,
                    t1.bl_handling_agent,
                    t1.is_transhipment,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.client_name = %s
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d'), customer_name]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条记录")
        
        if not all_records:
            # 如果没有数据，返回空结果结构
            results = []
            current_date = start_date
            for month_offset in range(month_count):
                month_data = {
                    "year": f"{current_date.year}",
                    "month": f"{current_date.month:02d}",
                    "pro2_system_id": pro2_system_id,
                    "customer_name": customer_name,
                    "data": [{
                        "pod_code": "无数据",
                        "bkbl_count": 0,
                        "rt": 0,
                        "teu": 0,
                        "air_weight": 0,
                        "income": 0,
                        "cost": 0,
                        "profit": 0,
                        "profit_rate": 0,
                        "all_nominated_count": 0,
                        "all_nominated_rt": 0,
                        "all_nominated_teu": 0,
                        "all_nominated_profit": 0,
                        "nomi_agent_count": 0
                    }]
                }
                results.append(month_data)
                current_date = current_date + relativedelta(months=1)
            return results
        
        # 应用特例处理并按月份和目的港分组
        monthly_pod_data = defaultdict(lambda: defaultdict(list))
        
        for record in all_records:
            # 应用特例处理
            processed_record = apply_special_case_processing(record)
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            
            # 获取卸货港，应用特例处理后的值
            pod_code = processed_record.get('bill_pod', '') or ''
            if not pod_code:
                pod_code = '未知港口'
            
            # 按月份和目的港分组数据
            monthly_pod_data[year_month][pod_code].append(processed_record)
        
        # 构建结果
        results = []
        current_date = start_date
        
        for month_offset in range(month_count):
            year_month = f"{current_date.year}-{current_date.month:02d}"
            
            # 获取该月的数据
            pod_data_dict = monthly_pod_data.get(year_month, {})
            
            month_data = {
                "year": f"{current_date.year}",
                "month": f"{current_date.month:02d}",
                "pro2_system_id": pro2_system_id,
                "customer_name": customer_name,
                "data": []
            }
            
            if pod_data_dict:
                # 计算各卸货港的汇总数据
                for pod_code, records in pod_data_dict.items():
                    aggregated_data = calculate_aggregated_data(records)
                    
                    # 构建目的港数据
                    pod_result = {
                        "pod_code": pod_code,
                        **aggregated_data
                    }
                    
                    # 移除不相关的字段（这些字段在客户维度分析中不适用）
                    pod_result.pop('customer_count', None)  # 客户维度分析不需要客户数量
                    
                    month_data["data"].append(pod_result)
                
                # 按卸货港代码排序
                month_data["data"].sort(key=lambda x: x["pod_code"])
            else:
                # 如果该月没有数据，添加空数据记录
                empty_data = calculate_aggregated_data([])
                empty_data.pop('customer_count', None)
                month_data["data"].append({
                    "pod_code": "无数据",
                    **empty_data
                })
            
            results.append(month_data)
            current_date = current_date + relativedelta(months=1)
        
        return results
        
    except Exception as e:
        print(f"高性能分析客户数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

# 常用场景4: 分析连续n个月利润前n大客户的数据(Booking分析） - 检查ok
async def optimized_analysis_booking_by_top_n_customers(begin_date: str, month_count: int, top_n: int = 5, pro2_system_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    高性能优化版本: 分析连续n个月利润前n大客户的数据(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 在内存中进行客户排序和数据聚合，减少数据库负载
    4. 优化数据结构和处理流程
    
    Args:
        begin_date: 开始日期，格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        top_n: 前n大客户数量，不允许超过10
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含前n名客户的月度分析数据
    """
    try:
        # 参数验证
        if top_n > 10:
            raise ValueError("top_n不允许超过10")
        
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询前{top_n}大客户: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数一次性查询所有数据，排除指定货（is_freehand=0）
        sql = """
            SELECT 
                job_date,
                client_name,
                bill_pod,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                is_freehand,
                salesman_name,
                salesman_department,
                job_handling_agent,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_date,
                    t1.client_name,
                    t1.bill_pod,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_department,
                    t1.job_handling_agent,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.is_freehand != 0
                AND t1.client_name IS NOT NULL 
                AND t1.client_name != ''
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条记录")
        
        if not all_records:
            return []
        
        # 应用特例处理并按月份和客户分组
        monthly_customer_data = defaultdict(lambda: defaultdict(list))
        monthly_customer_profit = defaultdict(lambda: defaultdict(float))
        
        for record in all_records:
            # 应用特例处理
            processed_record = apply_special_case_processing(record)
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            customer_name = processed_record['client_name'].strip()
            
            # 按月份和客户分组数据
            monthly_customer_data[year_month][customer_name].append(processed_record)
            
            # 累计客户在该月的利润
            profit = float(processed_record.get('profit', 0) or 0)
            monthly_customer_profit[year_month][customer_name] += profit
        
        # 收集所有月份的前n大客户
        all_top_customers = set()
        
        for year_month, customer_profits in monthly_customer_profit.items():
            # 按利润排序，取前n名
            sorted_customers = sorted(customer_profits.items(), key=lambda x: x[1], reverse=True)[:top_n]
            for customer_name, _ in sorted_customers:
                all_top_customers.add(customer_name)
        
        print(f"找到前{top_n}大客户共 {len(all_top_customers)} 个")
        
        if not all_top_customers:
            return []
        
        # 构建结果
        results = []
        
        for customer_name in sorted(all_top_customers):
            customer_result = {
                "customer_name": customer_name,
                "pro2_system_id": pro2_system_id,
                "data": []
            }
            
            # 为每个月生成数据
            current_date = start_date
            for month_offset in range(month_count):
                year_month = f"{current_date.year}-{current_date.month:02d}"
                
                # 获取该客户该月的数据
                customer_records = monthly_customer_data[year_month].get(customer_name, [])
                
                if customer_records:
                    # 计算目的港数量（去重）
                    pod_set = set()
                    for record in customer_records:
                        pod_code = record.get('bill_pod', '') or ''
                        if pod_code:
                            pod_set.add(pod_code)
                    
                    # 计算汇总数据
                    stats = _calculate_records_stats(customer_records)
                    
                    month_data = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "pod_count": len(pod_set),
                        "bkbl_count": stats["bkbl_count"],
                        "rt": stats["rt"],
                        "teu": stats["teu"],
                        "air_weight": stats["air_weight"],
                        "income": stats["income"],
                        "cost": stats["cost"],
                        "profit": stats["profit"],
                        "profit_rate": stats["profit_rate"],
                    }
                else:
                    # 如果该月没有数据，添加空数据
                    month_data = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "pod_count": 0,
                        "bkbl_count": 0,
                        "rt": 0,
                        "teu": 0,
                        "air_weight": 0,
                        "income": 0,
                        "cost": 0,
                        "profit": 0,
                        "profit_rate": 0,
                    }
                
                customer_result["data"].append(month_data)
                current_date = current_date + relativedelta(months=1)
            
            results.append(customer_result)
        
        return results
        
    except Exception as e:
        print(f"高性能分析前n大客户数据时发生错误: {str(e)}")
        traceback.print_exc()
        return []

# 常用场景5: 分析连续n个月集拼航线的数据(Job分析) - 检查ok
async def optimized_analysis_job_consol_line(begin_date: str, month_count: int, pro2_system_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    优化版本: 分析连续n个月集拼航线的数据(Job分析)
    
    主要优化:
    1. 一次性查询所有数据，避免嵌套循环查询
    2. 在内存中进行分组统计，而不是多次查询数据库
    3. 简化SQL查询，只获取必要字段
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"优化查询: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 一次性查询所有集拼数据
        sql = """
            SELECT 
                t1.job_type_cn,
                t1.job_date,
                t1.pol_code,
                t1.pod_code,
                t1.bk_count,
                t1.bill_count,
                t1.rt,
                t1.income,
                t1.cost,
                t1.profit,
                t1.consolidation_20,
                t1.consolidation_40,
                t1.nomi_count,
                t1.nomi_rt
            FROM t_job_details t1
            WHERE t1.job_date >= %s AND t1.job_date <= %s
            AND t1.is_consolidation = 1
            AND t1.pol_code IS NOT NULL 
            AND t1.pod_code IS NOT NULL
            AND t1.pol_code != ''
            AND t1.pod_code != ''
            AND t1.id = (
                SELECT MAX(t2.id) 
                FROM t_job_details t2 
                WHERE t2.job_id = t1.job_id
                                AND t2.pro2_system_id = t1.pro2_system_id
            )
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        print("执行数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条记录")
        
        # 在内存中按集拼航线和月份分组
        consol_data = defaultdict(lambda: defaultdict(list))
        
        for record in all_records:
            job_date = record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            pol_code = record['pol_code']
            pod_code = record['pod_code']
            consol_line = f"{pol_code}/{pod_code}"
            
            consol_data[consol_line][year_month].append(record)
        
        print(f"分组完成，找到 {len(consol_data)} 条集拼航线")
        
        # 构建结果
        results = []
        
        for consol_line, month_data in consol_data.items():
            line_result = {
                "pro2_system_id": pro2_system_id,
                "consol_line": consol_line,
                "data": []
            }
            
            # 为每个月生成数据
            current_date = start_date
            for month_offset in range(month_count):
                year_month = f"{current_date.year}-{current_date.month:02d}"
                records = month_data.get(year_month, [])
                
                # 计算汇总数据
                if records:
                    # 根据业务类型确定票数计算方式
                    business_type = records[0].get('job_type_cn', '')
                    if business_type == '海运出口':
                        total_count = sum(int(r.get('bk_count', 0) or 0) for r in records)
                    else:
                        total_count = sum(int(r.get('bill_count', 0) or 0) for r in records)
                    
                    total_income = sum(float(r.get('income', 0) or 0) for r in records)
                    total_cost = abs(sum(float(r.get('cost', 0) or 0) for r in records))
                    total_profit = sum(float(r.get('profit', 0) or 0) for r in records)
                    total_rt = sum(float(r.get('rt', 0) or 0) for r in records)
                    
                    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
                    
                    month_result = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "bkbl_count": total_count,
                        "rt": total_rt,
                        "income": total_income,
                        "cost": total_cost,
                        "profit": total_profit,
                        "profit_rate": round(profit_rate, 2),
                        "consol_rt": total_rt,  # 集拼数据的RT就是总RT
                        "consol_20_count": sum(int(r.get('consolidation_20', 0) or 0) for r in records),
                        "consol_40_count": sum(int(r.get('consolidation_40', 0) or 0) for r in records),
                        "all_nominated_count": sum(int(r.get('nomi_count', 0) or 0) for r in records),
                        "all_nominated_rt": sum(float(r.get('nomi_rt', 0) or 0) for r in records),
                        "port_nominated_count": 0,  # 暂不实现
                        "port_nominated_rt": 0,     # 暂不实现
                    }
                else:
                    # 空数据
                    month_result = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "bkbl_count": 0,
                        "rt": 0,
                        "income": 0,
                        "cost": 0,
                        "profit": 0,
                        "profit_rate": 0,
                        "consol_rt": 0,
                        "consol_20_count": 0,
                        "consol_40_count": 0,
                        "all_nominated_count": 0,
                        "all_nominated_rt": 0,
                        "port_nominated_count": 0,
                        "port_nominated_rt": 0,
                    }
                
                line_result["data"].append(month_result)
                current_date = current_date + relativedelta(months=1)
            
            results.append(line_result)
        
        # 按集拼航线名称排序
        results.sort(key=lambda x: x["consol_line"])
        
        return results
        
    except Exception as e:
        print(f"优化分析集拼航线数据时发生错误: {str(e)}")
        traceback.print_exc()
        return []

# 常用场景6: 分析连续n个月票数最多的n个指定货代理(Booking分析) - 检查ok
async def optimized_analysis_booking_nomi_agents(begin_date: str, month_count: int, top_n: int = 10, pro2_system_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    真正的高性能优化版本: 分析连续n个月票数最多的n个指定货代理(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 在内存中进行代理排序和数据聚合，减少数据库负载
    4. 优化数据结构和处理流程
    
    Args:
        begin_date: 开始日期，格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        top_n: 前n大代理数量，默认10
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含前n名指定货代理的月度分析数据
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询指定货代理: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 对于86021需要特殊处理，不能直接用is_freehand=0过滤
        if pro2_system_id == 86021:
            # 对于86021，需要查询所有记录然后在内存中应用特例规则
            sql = """
                SELECT 
                    job_date,
                    client_name,
                    lcl_rt,
                    teu,
                    air_weight,
                    income,
                    cost,
                    profit,
                    is_freehand,
                    salesman_name,
                    salesman_department,
                    job_handling_agent,
                    pro2_system_id
                FROM (
                    SELECT 
                        t1.job_date,
                        t1.client_name,
                        t1.lcl_rt,
                        t1.teu,
                        t1.air_weight,
                        t1.income,
                        t1.cost,
                        t1.profit,
                        t1.is_freehand,
                        t1.salesman_name,
                        t1.salesman_department,
                        t1.job_handling_agent,
                        t1.pro2_system_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                            ORDER BY t1.id DESC
                        ) as rn
                    FROM t_booking_details t1
                    WHERE t1.job_date >= %s AND t1.job_date <= %s
                    AND t1.pro2_system_id = %s
                    AND (t1.is_freehand = 0 OR (t1.is_freehand = 1 AND t1.salesman_department = '指定货业务'))
            """
            params = [begin_date, end_date.strftime('%Y-%m-%d'), pro2_system_id]
        else:
            # 其他分公司正常查询指定货记录
            sql = """
                SELECT 
                    job_date,
                    client_name,
                    lcl_rt,
                    teu,
                    air_weight,
                    income,
                    cost,
                    profit,
                    is_freehand,
                    salesman_name,
                    salesman_department,
                    job_handling_agent,
                    pro2_system_id
                FROM (
                    SELECT 
                        t1.job_date,
                        t1.client_name,
                        t1.lcl_rt,
                        t1.teu,
                        t1.air_weight,
                        t1.income,
                        t1.cost,
                        t1.profit,
                        t1.is_freehand,
                        t1.salesman_name,
                        t1.salesman_department,
                        t1.job_handling_agent,
                        t1.pro2_system_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                            ORDER BY t1.id DESC
                        ) as rn
                    FROM t_booking_details t1
                    WHERE t1.job_date >= %s AND t1.job_date <= %s
                    AND t1.is_freehand = 0
            """
            params = [begin_date, end_date.strftime('%Y-%m-%d')]
            
            # 添加分公司过滤条件
            if pro2_system_id is not None:
                sql += " AND t1.pro2_system_id = %s"
                params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条指定货记录")
        
        if not all_records:
            return []
        
        # 处理指定货代理名称并按月份和代理分组
        monthly_agent_data = defaultdict(lambda: defaultdict(list))
        monthly_agent_tickets = defaultdict(lambda: defaultdict(int))
        
        processed_count = 0
        for record in all_records:
            # 对于86021，需要应用特例处理逻辑
            if pro2_system_id == 86021:
                # 应用特例处理，确定记录是否真正为指定货
                processed_record = apply_special_case_processing(record)
                
                # 只有经过特例处理后真正为指定货的记录才统计
                if processed_record.get('is_freehand') != 0:
                    continue  # 跳过非指定货记录
                
                # 获取经过特例处理的指定货代理名称
                nomi_agent_name = processed_record.get('nomi_agent_name', '') or ''
            else:
                # 其他分公司直接使用job_handling_agent作为指定货代理
                nomi_agent_name = record.get('job_handling_agent', '') or ''
                processed_record = record
            
            # 处理有效的指定货代理记录
            if nomi_agent_name.strip():
                agent_name = nomi_agent_name.strip()
                processed_record['nomi_agent_name'] = agent_name
                
                job_date = processed_record['job_date']
                year_month = f"{job_date.year}-{job_date.month:02d}"
                
                # 按月份和代理分组数据
                monthly_agent_data[year_month][agent_name].append(processed_record)
                
                # 累计代理在该月的票数
                monthly_agent_tickets[year_month][agent_name] += 1
                processed_count += 1
        
        print(f"处理后有效记录: {processed_count} 条")
        
        if processed_count == 0:
            return []
        
        # 收集所有月份的前n大代理
        all_top_agents = set()
        
        for year_month, agent_tickets in monthly_agent_tickets.items():
            # 按票数排序，取前n名
            sorted_agents = sorted(agent_tickets.items(), key=lambda x: x[1], reverse=True)[:top_n]
            for agent_name, _ in sorted_agents:
                all_top_agents.add(agent_name)
        
        print(f"找到前{top_n}大代理共 {len(all_top_agents)} 个")
        
        if not all_top_agents:
            return []
        
        # 构建结果
        results = []
        
        for agent_name in sorted(all_top_agents):
            agent_result = {
                "nomi_agent_name": agent_name,
                "pro2_system_id": pro2_system_id,
                "data": []
            }
            
            # 为每个月生成数据
            current_date = start_date
            for month_offset in range(month_count):
                year_month = f"{current_date.year}-{current_date.month:02d}"
                
                # 获取该代理该月的数据
                agent_records = monthly_agent_data[year_month].get(agent_name, [])
                
                if agent_records:
                    # 计算客户数量（去重）
                    customers = set()
                    for r in agent_records:
                        client_name = r.get('client_name', '')
                        if client_name and client_name.strip():
                            customers.add(client_name.strip())
                    
                    # 计算汇总数据
                    total_income = sum(float(r.get('income', 0) or 0) for r in agent_records)
                    total_cost = abs(sum(float(r.get('cost', 0) or 0) for r in agent_records))
                    total_profit = sum(float(r.get('profit', 0) or 0) for r in agent_records)
                    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
                    
                    month_result = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "customer_count": len(customers),
                        "bkbl_count": len(agent_records),
                        "rt": sum(float(r.get('lcl_rt', 0) or 0) for r in agent_records),
                        "teu": sum(float(r.get('teu', 0) or 0) for r in agent_records),
                        "air_weight": sum(float(r.get('air_weight', 0) or 0) for r in agent_records),
                        "income": total_income,
                        "cost": total_cost,
                        "profit": total_profit,
                        "profit_rate": round(profit_rate, 2),
                    }
                else:
                    # 空数据
                    month_result = {
                        "year": f"{current_date.year}",
                        "month": f"{current_date.month:02d}",
                        "customer_count": 0,
                        "bkbl_count": 0,
                        "rt": 0,
                        "teu": 0,
                        "air_weight": 0,
                        "income": 0,
                        "cost": 0,
                        "profit": 0,
                        "profit_rate": 0,
                    }
                
                agent_result["data"].append(month_result)
                current_date = current_date + relativedelta(months=1)
            
            results.append(agent_result)
        
        return results
        
    except Exception as e:
        print(f"高性能分析指定货代理数据时发生错误: {str(e)}")
        traceback.print_exc()
        return []

# 常用场景7: 分析连续n个月RT最高的n个目的港(Booking分析) - 检查ok
async def optimized_analysis_booking_top_n_rt(begin_date: str, month_count: int, top_n: int = 10, pro2_system_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    高性能优化版本: 分析连续n个月RT最高的n个目的港(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 减少内存中的数据处理步骤
    3. 优化数据结构和计算逻辑
    
    Args:
        begin_date: 开始日期，格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        top_n: 前n名目的港数量，默认10
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含前n名目的港的月度分析数据
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询前{top_n}名目的港RT: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数优化的SQL查询，避免相关子查询
        sql = """
            SELECT 
                job_date,
                bill_pod,
                client_name,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                is_freehand,
                salesman_name,
                salesman_department,
                job_handling_agent,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_date,
                    t1.bill_pod,
                    t1.client_name,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_department,
                    t1.job_handling_agent,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
                AND t1.bill_pod IS NOT NULL 
                AND t1.bill_pod != ''
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行优化的数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条记录")
        
        if not all_records:
            return []
        
        # 快速预处理数据并按目的港分组
        pod_monthly_data = defaultdict(lambda: defaultdict(lambda: {'non_nomi': [], 'nomi': []}))
        monthly_pod_rt = defaultdict(lambda: defaultdict(float))
        
        for record in all_records:
            # 应用特例处理
            processed_record = record.copy()
            
            # 处理86021的特殊情况
            if record.get('pro2_system_id') == 86021:
                salesman_dept = record.get('salesman_department', '')
                if record.get('is_freehand') == 1 and salesman_dept == '指定货业务':
                    processed_record['is_freehand'] = 0
            
            # 确保nomi_agent_name字段存在
            processed_record['nomi_agent_name'] = record.get('job_handling_agent', '') or ''
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            pod_code = processed_record['bill_pod']
            
            # 按目的港和月份分组
            if processed_record.get('is_freehand') == 0:  # 指定货
                pod_monthly_data[pod_code][year_month]['nomi'].append(processed_record)
            else:  # 非指定货
                pod_monthly_data[pod_code][year_month]['non_nomi'].append(processed_record)
                # 同时计算RT用于排序
                rt = float(processed_record.get('lcl_rt', 0) or 0)
                monthly_pod_rt[year_month][pod_code] += rt
        
        # 找出前n大目的港（基于非指定货RT）
        all_top_pods = set()
        for year_month, pod_rt in monthly_pod_rt.items():
            sorted_pods = sorted(pod_rt.items(), key=lambda x: x[1], reverse=True)[:top_n]
            for pod_code, _ in sorted_pods:
                all_top_pods.add(pod_code)
        
        print(f"找到前{top_n}大目的港共 {len(all_top_pods)} 个: {sorted(all_top_pods)}")
        
        if not all_top_pods:
            return []
        
        # 构建结果
        results = []
        
        for pod_code in sorted(all_top_pods):
            pod_result = {
                "pod_code": pod_code,
                "pro2_system_id": pro2_system_id,
                "data": []
            }
            
            # 为每个月生成数据
            current_date = start_date
            for month_offset in range(month_count):
                year_month = f"{current_date.year}-{current_date.month:02d}"
                
                # 获取该目的港该月的数据
                non_nomi_records = pod_monthly_data[pod_code].get(year_month, {}).get('non_nomi', [])
                nomi_records = pod_monthly_data[pod_code].get(year_month, {}).get('nomi', [])
                
                # 计算非指定货数据（主要数据）
                main_data = _calculate_records_stats(non_nomi_records)
                
                # 计算指定货数据
                nomi_data = _calculate_nominated_stats(nomi_records)
                
                # 合并数据
                month_result = {
                    "year": f"{current_date.year}",
                    "month": f"{current_date.month:02d}",
                    **main_data,
                    **nomi_data
                }
                
                pod_result["data"].append(month_result)
                current_date = current_date + relativedelta(months=1)
            
            results.append(pod_result)
        
        return results
        
    except Exception as e:
        print(f"高性能分析前n名目的港RT数据时发生错误: {str(e)}")
        traceback.print_exc()
        return []

# 工具函数：从数据库表提取海运空运损益数据（含转运）- 不带缓存
async def get_booking_details_from_tokens_table(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    从 mcp_tokens.t_booking_details 表根据时间周期查询海运空运损益并添加转运利润字段
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD'
        end_date: 结束日期, 格式为'YYYY-MM-DD'
        pro2_system_id: 分公司代码, 可选值为: 86532-QDO(青岛), 86021-SHA(上海), 852-HKG(香港), 8103-TKY(东京), 如果为空则提取所有分公司
        
    Returns:
        Dict[str, Any]: 包含数据列表和查询信息的字典
    """
    try:
        print(f"开始从tokens表查询booking数据（含转运）: {begin_date} 到 {end_date}, 分公司: {pro2_system_id or '全部'}")
        
        # 构建SQL查询，使用子查询处理去重逻辑
        # 对于相同的 job_id + bkbl_id，只取 id 最大的记录
        sql = """
            SELECT 
                job_type_cn,
                job_date,
                job_no,
                bkbl_no,
                client_name,
                vessel,
                voyage,
                job_pol,
                bill_pol,
                bill_pod,
                service_mode,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                is_freehand,
                salesman_name,
                salesman_id,
                salesman_department,
                operator_name,
                operator_department,
                coloader_name,
                job_handling_agent,
                bl_handling_agent,
                is_transhipment,
                transhipment_id,
                bkbl_id,
                job_id,
                job_type_id,
                operator_id,
                pro2_system_id,
                data_hash
            FROM t_booking_details t1
            WHERE t1.job_date >= %s 
            AND t1.job_date <= %s
            AND t1.id = (
                SELECT MAX(t2.id) 
                FROM t_booking_details t2 
                WHERE t2.job_id = t1.job_id 
                AND t2.bkbl_no = t1.bkbl_no
                AND t2.pro2_system_id = t1.pro2_system_id
            )
        """
        
        params = [begin_date, end_date]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += " ORDER BY t1.job_date, t1.job_no, t1.bkbl_no"
        
        # 执行查询
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                raw_records = await cursor.fetchall()
        
        # 处理特例情况
        processed_records = []
        for record in raw_records:
            processed_record = apply_special_case_processing(record)
            processed_records.append(processed_record)
        
        print(f"从tokens表查询booking数据完成（含转运）: {len(processed_records)} 条记录")
        
        return {
            'data': processed_records,
            'total_count': len(processed_records),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_booking_details',
                'pro2_system_id': pro2_system_id,
                'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司'
            }
        }
        
    except Exception as e:
        print(f"从tokens表查询booking数据失败（含转运）: {e}")
        import traceback
        traceback.print_exc()
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_booking_details',
                'pro2_system_id': pro2_system_id,
                'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司',
                'error': f'查询失败: {str(e)}'
            }
        }

# 工具函数：从数据库表提取作业明细数据（含转运）- 不带缓存
async def get_job_details_from_tokens_table(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    从 mcp_tokens.t_job_details 表根据时间周期查询作业明细并添加转运利润字段
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD'
        end_date: 结束日期, 格式为'YYYY-MM-DD'
        pro2_system_id: 分公司代码, 可选值为: 86532-QDO(青岛), 86021-SHA(上海), 852-HKG(香港), 8103-TKY(东京), 如果为空则提取所有分公司
        
    Returns:
        Dict[str, Any]: 包含数据列表和查询信息的字典
    """
    try:
        print(f"开始从tokens表查询job数据（含转运）: {begin_date} 到 {end_date}, 分公司: {pro2_system_id or '全部'}")
        
        # 构建SQL查询，使用子查询处理去重逻辑
        # 对于相同的 job_id，只取 id 最大的记录
        sql = """
            SELECT 
                job_type_cn,
                job_date,
                job_no,
                vessel,
                voyage,
                pol_code,
                pod_code,
                bk_count,
                rt,
                teu,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                operator_name,
                job_handling_agent,
                nomi_count,
                nomi_rt,
                is_consolidation,
                bill_count,
                consolidation_20,
                consolidation_40,
                operator_department,
                is_op_finished,
                is_checked,
                job_id,
                job_type_id,
                operator_id,
                etd_date,
                eta_date,
                pro2_system_id,
                data_hash
            FROM t_job_details t1
            WHERE t1.job_date >= %s 
            AND t1.job_date <= %s
            AND t1.id = (
                SELECT MAX(t2.id) 
                FROM t_job_details t2 
                WHERE t2.job_id = t1.job_id
                                AND t2.pro2_system_id = t1.pro2_system_id
            )
        """
        
        params = [begin_date, end_date]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += " ORDER BY t1.job_date, t1.job_no"
        
        # 执行查询
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                raw_records = await cursor.fetchall()
        
        # 处理特例情况
        processed_records = []
        for record in raw_records:
            processed_record = apply_special_case_processing_job(record)
            processed_records.append(processed_record)
        
        # 如果没有查询到数据
        if not processed_records:
            print(f"从tokens表未查询到job数据: {begin_date} 到 {end_date}")
            return {
                'data': [],
                'total_count': 0,
                'query_info': {
                    'date_range': f'{begin_date} 到 {end_date}',
                    'data_type': '全部作业明细数据（含转运）- 从tokens表提取',
                    'source_table': 'mcp_tokens.t_job_details',
                    'pro2_system_id': pro2_system_id,
                    'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司',
                    'message': '未查询到数据'
                }
            }
        
        print(f"从tokens表查询job数据完成（含转运）: {len(processed_records)} 条记录")
        
        return {
            'data': processed_records,
            'total_count': len(processed_records),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_job_details',
                'pro2_system_id': pro2_system_id,
                'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司'
            }
        }
        
    except Exception as e:
        print(f"从tokens表查询job数据失败（含转运）: {e}")
        import traceback
        traceback.print_exc()
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_job_details',
                'pro2_system_id': pro2_system_id,
                'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司',
                'error': f'查询失败: {str(e)}'
            }
        }

# 新增函数：带缓存的从tokens表提取booking数据
@async_cached_data_function
async def get_sea_air_profit_from_tokens_table_cached(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    根据时间周期从tokens表查询海运空运损益并添加转运利润字段 (调度器使用)
    使用 get_booking_details_from_tokens_table 函数直接获取包含转运功能的业务明细数据
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD'
        end_date: 结束日期, 格式为'YYYY-MM-DD'
        pro2_system_id: 分公司代码, 可选值为: 86532-QDO(青岛), 86021-SHA(上海), 852-HKG(香港), 8103-TKY(东京), 如果为空则提取所有分公司
    """
    print(f"开始从tokens表查询海空损益数据（含转运）: {begin_date} 到 {end_date}, 分公司: {pro2_system_id or '全部'}")
    
    try:
        # 直接调用新的函数获取数据
        results = await get_booking_details_from_tokens_table(begin_date, end_date, pro2_system_id)
        
        print(f"从tokens表海空损益数据查询完成（含转运）: {results['total_count']} 条记录")
        
        return results
        
    except Exception as e:
        print(f"从tokens表查询海空损益数据失败（含转运）: {e}")
        import traceback
        traceback.print_exc()
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_booking_details',
                'error': f'查询失败: {str(e)}'
            }
        }

# 新增函数：带缓存的从tokens表提取job数据
@async_cached_data_function
async def get_job_details_from_tokens_table_cached(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    根据时间周期从tokens表查询作业明细并添加转运利润字段 (调度器使用)
    使用 get_job_details_from_tokens_table 函数直接获取包含转运功能的作业明细数据
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD'
        end_date: 结束日期, 格式为'YYYY-MM-DD'
        pro2_system_id: 分公司代码, 可选值为: 86532-QDO(青岛), 86021-SHA(上海), 852-HKG(香港), 8103-TKY(东京), 如果为空则提取所有分公司
    """
    print(f"开始从tokens表查询作业明细数据（含转运）: {begin_date} 到 {end_date}, 分公司: {pro2_system_id or '全部'}")
    
    try:
        # 直接调用新的函数获取数据
        results = await get_job_details_from_tokens_table(begin_date, end_date, pro2_system_id)
        
        print(f"从tokens表作业明细数据查询完成（含转运）: {results['total_count']} 条记录")
        
        return results
        
    except Exception as e:
        print(f"从tokens表查询作业明细数据失败（含转运）: {e}")
        import traceback
        traceback.print_exc()
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）- 从tokens表提取',
                'source_table': 'mcp_tokens.t_job_details',
                'error': f'查询失败: {str(e)}'
            }
        }

def _calculate_records_stats(records: List[Dict[str, Any]]) -> Dict[str, Any]:
    """计算记录统计数据的辅助函数"""
    if not records:
        return {
            "customer_count": 0,
            "bkbl_count": 0,
            "rt": 0,
            "teu": 0,
            "air_weight": 0,
            "income": 0,
            "cost": 0,
            "profit": 0,
            "profit_rate": 0,
        }
    
    # 计算客户数量（去重）
    customers = set()
    for r in records:
        client_name = r.get('client_name', '')
        if client_name and client_name.strip():
            customers.add(client_name.strip())
    
    total_income = sum(float(r.get('income', 0) or 0) for r in records)
    total_cost = abs(sum(float(r.get('cost', 0) or 0) for r in records))
    total_profit = sum(float(r.get('profit', 0) or 0) for r in records)
    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
    
    return {
        "customer_count": len(customers),
        "bkbl_count": len(records),
        "rt": sum(float(r.get('lcl_rt', 0) or 0) for r in records),
        "teu": sum(float(r.get('teu', 0) or 0) for r in records),
        "air_weight": sum(float(r.get('air_weight', 0) or 0) for r in records),
        "income": total_income,
        "cost": total_cost,
        "profit": total_profit,
        "profit_rate": round(profit_rate, 2),
    }

def _calculate_nominated_stats(records: List[Dict[str, Any]]) -> Dict[str, Any]:
    """计算指定货统计数据的辅助函数"""
    if not records:
        return {
            "all_nominated_customer_count": 0,
            "all_nominated_count": 0,
            "all_nominated_rt": 0,
            "all_nominated_teu": 0,
            "all_nominated_air_weight": 0,
            "all_nominated_income": 0,
            "all_nominated_cost": 0,
            "all_nominated_profit": 0,
            "all_nominated_profit_rate": 0,
        }
    
    customers = set()
    for r in records:
        client_name = r.get('client_name', '')
        if client_name and client_name.strip():
            customers.add(client_name.strip())
    
    income = sum(float(r.get('income', 0) or 0) for r in records)
    cost = abs(sum(float(r.get('cost', 0) or 0) for r in records))
    profit = sum(float(r.get('profit', 0) or 0) for r in records)
    profit_rate = (profit / income * 100) if income > 0 else 0
    
    return {
        "all_nominated_customer_count": len(customers),
        "all_nominated_count": len(records),
        "all_nominated_rt": sum(float(r.get('lcl_rt', 0) or 0) for r in records),
        "all_nominated_teu": sum(float(r.get('teu', 0) or 0) for r in records),
        "all_nominated_air_weight": sum(float(r.get('air_weight', 0) or 0) for r in records),
        "all_nominated_income": income,
        "all_nominated_cost": cost,
        "all_nominated_profit": profit,
        "all_nominated_profit_rate": round(profit_rate, 2),
    }

# 优化版本: 分析连续n月每个月的数据(Booking分析)
async def optimized_analysis_booking_on_month_by_month_data(begin_date: str, month_count: int, pro2_system_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    高性能优化版本: 分析连续n月每个月的数据(Booking分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 在内存中进行月份和业务类型分组，减少数据库负载
    4. 正确处理86021特例规则
    5. 修正：完全对齐原始版本的查询逻辑，确保数据一致性
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含连续n月每个月的数据(Booking分析），包括各业务类型的指标
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期 - 使用与原始版本相同的计算方式
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询Booking月度数据: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数优化的SQL查询 - 完全对齐原始版本的查询逻辑
        # 重要修正：移除所有额外的过滤条件，完全匹配原始版本
        sql = """
            SELECT 
                job_type_cn,
                job_date,
                job_no,
                bkbl_no,
                client_name,
                vessel,
                voyage,
                job_pol,
                bill_pol,
                bill_pod,
                service_mode,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                is_freehand,
                salesman_name,
                salesman_department,
                operator_name,
                operator_department,
                coloader_name,
                job_handling_agent,
                bl_handling_agent,
                is_transhipment,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_type_cn,
                    t1.job_date,
                    t1.job_no,
                    t1.bkbl_no,
                    t1.client_name,
                    t1.vessel,
                    t1.voyage,
                    t1.job_pol,
                    t1.bill_pol,
                    t1.bill_pod,
                    t1.service_mode,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_department,
                    t1.operator_name,
                    t1.operator_department,
                    t1.coloader_name,
                    t1.job_handling_agent,
                    t1.bl_handling_agent,
                    t1.is_transhipment,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
        """
        
        params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = %s"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能数据库查询...")
        print(f"SQL查询参数: {params}")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql, params)
                all_records = await cursor.fetchall()
        
        print(f"查询完成，获得 {len(all_records)} 条记录")
        
        if not all_records:
            # 如果没有数据，返回空结果结构
            results = []
            current_date = start_date
            for month_offset in range(month_count):
                month_data = {
                    "year": f"{current_date.year}",
                    "month": f"{current_date.month:02d}",
                    "pro2_system_id": pro2_system_id,
                    "sea_export_data": [calculate_aggregated_data([])],
                    "sea_import_data": [calculate_aggregated_data([])],
                    "triangle_trade_data": [calculate_aggregated_data([])],
                    "air_data": []
                }
                # 空运数据移除TEU相关字段
                air_data = calculate_aggregated_data([]).copy()
                air_data.pop('teu', None)
                air_data.pop('all_nominated_rt', None)
                month_data["air_data"] = [air_data]
                
                results.append(month_data)
                current_date = current_date + relativedelta(months=1)
            return results
        
        # 应用特例处理并按月份和业务类型分组
        monthly_business_data = defaultdict(lambda: defaultdict(list))
        
        for record in all_records:
            # 应用特例处理
            processed_record = apply_special_case_processing(record)
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            business_type = processed_record.get('job_type_cn', '')
            
            # 按月份和业务类型分组数据
            monthly_business_data[year_month][business_type].append(processed_record)
        
        print(f"数据按月分组完成，共处理 {len(monthly_business_data)} 个月份的数据")
        
        # 构建结果
        results = []
        current_date = start_date
        
        for month_offset in range(month_count):
            year_month = f"{current_date.year}-{current_date.month:02d}"
            
            # 获取该月的数据
            business_data_dict = monthly_business_data.get(year_month, {})
            
            month_data = {
                "year": f"{current_date.year}",
                "month": f"{current_date.month:02d}",
                "pro2_system_id": pro2_system_id,
                "sea_export_data": [],
                "sea_import_data": [],
                "triangle_trade_data": [],
                "air_data": []
            }
            
            # 调试输出
            month_record_count = sum(len(records) for records in business_data_dict.values())
            print(f"第{month_offset+1}月 ({year_month}) 的记录数: {month_record_count}")
            for business_type, records in business_data_dict.items():
                print(f"  {business_type}: {len(records)} 条记录")
            
            # 计算各业务类型的汇总数据
            for business_type in ['海运出口', '海运进口', '海运三角贸易', '空运']:
                records = business_data_dict.get(business_type, [])
                aggregated_data = calculate_aggregated_data(records)
                
                if business_type == '海运出口':
                    month_data["sea_export_data"].append(aggregated_data)
                elif business_type == '海运进口':
                    month_data["sea_import_data"].append(aggregated_data)
                elif business_type == '海运三角贸易':
                    month_data["triangle_trade_data"].append(aggregated_data)
                elif business_type == '空运':
                    # 空运数据不包括TEU
                    air_data = aggregated_data.copy()
                    air_data.pop('teu', None)
                    air_data.pop('all_nominated_rt', None)  # 空运不计算指定货RT
                    month_data["air_data"].append(air_data)
            
            # 如果某个业务类型没有数据，确保数组不为空
            for data_key in ["sea_export_data", "sea_import_data", "triangle_trade_data", "air_data"]:
                if not month_data[data_key]:
                    empty_data = calculate_aggregated_data([])
                    if data_key == "air_data":
                        empty_data.pop('teu', None)
                        empty_data.pop('all_nominated_rt', None)
                    month_data[data_key].append(empty_data)
            
            results.append(month_data)
            current_date = current_date + relativedelta(months=1)
        
        return results
        
    except Exception as e:
        print(f"高性能分析Booking月度数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

# 优化版本: 分析连续n月每个月的数据(Job分析)
async def optimized_analysis_job_on_month_by_month_data(begin_date: str, month_count: int, pro2_system_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    高性能优化版本: 分析连续n月每个月的数据(Job分析)
    
    主要优化:
    1. 使用窗口函数替代相关子查询，大幅提升SQL查询性能
    2. 一次性查询整个时间范围的数据，避免多次数据库查询
    3. 优化86021特例处理，合并Job和Booking数据获取
    4. 在内存中进行月份和业务类型分组，减少数据库负载
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD' --> 必须为每月1日
        month_count: 连续n月
        pro2_system_id: 分公司代码
    
    Returns:
        List[Dict], 包含连续n月每个月的数据(Job分析）
    """
    try:
        # 解析开始日期
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        if start_date.day != 1:
            raise ValueError("开始日期必须为每月1日")
        
        # 计算结束日期
        end_date = start_date + relativedelta(months=month_count) - timedelta(days=1)
        
        print(f"高性能查询Job月度数据: {begin_date} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 使用窗口函数优化的SQL查询Job表
        job_sql = """
            SELECT 
                job_type_cn,
                job_date,
                job_no,
                vessel,
                voyage,
                pol_code,
                pod_code,
                bk_count,
                rt,
                teu,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                operator_name,
                job_handling_agent,
                nomi_count,
                nomi_rt,
                is_consolidation,
                bill_count,
                consolidation_20,
                consolidation_40,
                operator_department,
                is_op_finished,
                is_checked,
                pro2_system_id
            FROM (
                SELECT 
                    t1.job_type_cn,
                    t1.job_date,
                    t1.job_no,
                    t1.vessel,
                    t1.voyage,
                    t1.pol_code,
                    t1.pod_code,
                    t1.bk_count,
                    t1.rt,
                    t1.teu,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.operator_name,
                    t1.job_handling_agent,
                    t1.nomi_count,
                    t1.nomi_rt,
                    t1.is_consolidation,
                    t1.bill_count,
                    t1.consolidation_20,
                    t1.consolidation_40,
                    t1.operator_department,
                    t1.is_op_finished,
                    t1.is_checked,
                    t1.pro2_system_id,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_job_details t1
                WHERE t1.job_date >= %s AND t1.job_date <= %s
        """
        
        job_params = [begin_date, end_date.strftime('%Y-%m-%d')]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            job_sql += " AND t1.pro2_system_id = %s"
            job_params.append(pro2_system_id)
        
        job_sql += """
            ) ranked_data
            WHERE rn = 1
        """
        
        print("执行高性能Job数据库查询...")
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(job_sql, job_params)
                job_records = await cursor.fetchall()
        
        print(f"Job查询完成，获得 {len(job_records)} 条记录")
        
        # 对于86021，需要优化的方式获取指定货数据
        job_nominated_map = {}
        if pro2_system_id == 86021 or (pro2_system_id is None and job_records):
            print("查询86021指定货数据...")
            
            # 确定需要查询的分公司
            target_pro2_system_id = 86021 if pro2_system_id == 86021 else None
            
            # 使用窗口函数优化的Booking查询
            booking_sql = """
                SELECT 
                    job_no,
                    is_freehand,
                    salesman_department,
                    lcl_rt,
                    teu,
                    pro2_system_id
                FROM (
                    SELECT 
                        t1.job_no,
                        t1.is_freehand,
                        t1.salesman_department,
                        t1.lcl_rt,
                        t1.teu,
                        t1.pro2_system_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                            ORDER BY t1.id DESC
                        ) as rn
                    FROM t_booking_details t1
                    WHERE t1.job_date >= %s AND t1.job_date <= %s
            """
            
            booking_params = [begin_date, end_date.strftime('%Y-%m-%d')]
            
            if target_pro2_system_id is not None:
                booking_sql += " AND t1.pro2_system_id = %s"
                booking_params.append(target_pro2_system_id)
            else:
                # 如果没有指定分公司，只查询86021的数据
                booking_sql += " AND t1.pro2_system_id = 86021"
            
            booking_sql += """
                ) ranked_data
                WHERE rn = 1
            """
            
            async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
                async with connection.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(booking_sql, booking_params)
                    booking_records = await cursor.fetchall()
            
            print(f"Booking查询完成，获得 {len(booking_records)} 条记录")
            
            # 处理86021特例并汇总指定货数据
            job_data = defaultdict(list)
            for record in booking_records:
                if record.get('pro2_system_id') == 86021:
                    # 应用特例处理
                    processed_record = apply_special_case_processing(record)
                    job_no = record['job_no']
                    job_data[job_no].append(processed_record)
                else:
                    # 其他分公司直接处理
                    job_no = record['job_no']
                    job_data[job_no].append(record)
            
            # 对每个Job汇总指定货数据
            for job_no, records in job_data.items():
                nominated_count = 0
                nominated_rt = 0
                nominated_teu = 0
                
                for record in records:
                    # 如果是指定货（is_freehand=0），则计入统计
                    if record.get('is_freehand') == 0:
                        nominated_count += 1
                        nominated_rt += float(record.get('lcl_rt', 0) or 0)
                        nominated_teu += float(record.get('teu', 0) or 0)
                
                if nominated_count > 0:
                    job_nominated_map[job_no] = {
                        'nominated_count': nominated_count,
                        'nominated_rt': nominated_rt,
                        'nominated_teu': nominated_teu
                    }
        
        if not job_records:
            # 如果没有数据，返回空结果结构
            results = []
            current_date = start_date
            for month_offset in range(month_count):
                month_data = {
                    "year": f"{current_date.year}",
                    "month": f"{current_date.month:02d}",
                    "pro2_system_id": pro2_system_id,
                    "sea_export_data": [calculate_job_aggregated_data([], "海运出口")],
                    "sea_import_data": [calculate_job_aggregated_data([], "海运进口")],
                    "triangle_trade_data": [calculate_job_aggregated_data([], "海运三角贸易")],
                    "air_data": []
                }
                # 空运数据移除相关字段
                air_data = calculate_job_aggregated_data([], "空运")
                for key in ['teu', 'all_nominated_rt', 'all_nominated_teu', 'consol_count', 'consol_rt', 'consol_20_count', 'consol_40_count']:
                    air_data.pop(key, None)
                month_data["air_data"].append(air_data)
                
                results.append(month_data)
                current_date = current_date + relativedelta(months=1)
            return results
        
        # 应用特例处理并按月份和业务类型分组
        monthly_business_data = defaultdict(lambda: defaultdict(list))
        
        for record in job_records:
            # 应用特例处理
            processed_record = apply_special_case_processing_job(record)
            
            # 如果是86021的数据，更新指定货信息
            if processed_record.get('pro2_system_id') == 86021:
                job_no = processed_record.get('job_no', '')
                if job_no in job_nominated_map:
                    nominated_data = job_nominated_map[job_no]
                    processed_record['nomi_count'] = nominated_data['nominated_count']
                    processed_record['nomi_rt'] = nominated_data['nominated_rt']
                    processed_record['nomi_teu'] = nominated_data['nominated_teu']
            
            job_date = processed_record['job_date']
            year_month = f"{job_date.year}-{job_date.month:02d}"
            business_type = processed_record.get('job_type_cn', '')
            
            # 按月份和业务类型分组数据
            monthly_business_data[year_month][business_type].append(processed_record)
        
        # 构建结果
        results = []
        current_date = start_date
        
        for month_offset in range(month_count):
            year_month = f"{current_date.year}-{current_date.month:02d}"
            
            # 获取该月的数据
            business_data_dict = monthly_business_data.get(year_month, {})
            
            month_data = {
                "year": f"{current_date.year}",
                "month": f"{current_date.month:02d}",
                "pro2_system_id": pro2_system_id,
                "sea_export_data": [],
                "sea_import_data": [],
                "triangle_trade_data": [],
                "air_data": []
            }
            
            # 计算各业务类型的汇总数据
            for business_type in ['海运出口', '海运进口', '海运三角贸易', '空运']:
                records = business_data_dict.get(business_type, [])
                aggregated_data = calculate_job_aggregated_data(records, business_type)
                
                if business_type == '海运出口':
                    month_data["sea_export_data"].append(aggregated_data)
                elif business_type == '海运进口':
                    month_data["sea_import_data"].append(aggregated_data)
                elif business_type == '海运三角贸易':
                    month_data["triangle_trade_data"].append(aggregated_data)
                elif business_type == '空运':
                    # 空运数据不包括TEU和集拼相关数据
                    air_data = aggregated_data.copy()
                    for key in ['teu', 'all_nominated_rt', 'all_nominated_teu', 'consol_count', 'consol_rt', 'consol_20_count', 'consol_40_count']:
                        air_data.pop(key, None)
                    month_data["air_data"].append(air_data)
            
            # 如果某个业务类型没有数据，确保数组不为空
            for data_key in ["sea_export_data", "sea_import_data", "triangle_trade_data", "air_data"]:
                if not month_data[data_key]:
                    if data_key == "sea_export_data":
                        empty_business_type = "海运出口"
                    elif data_key == "sea_import_data":
                        empty_business_type = "海运进口"
                    elif data_key == "triangle_trade_data":
                        empty_business_type = "海运三角贸易"
                    else:
                        empty_business_type = "空运"
                    
                    empty_data = calculate_job_aggregated_data([], empty_business_type)
                    if data_key == "air_data":
                        for key in ['teu', 'all_nominated_rt', 'all_nominated_teu', 'consol_count', 'consol_rt', 'consol_20_count', 'consol_40_count']:
                            empty_data.pop(key, None)
                    month_data[data_key].append(empty_data)
            
            results.append(month_data)
            current_date = current_date + relativedelta(months=1)
        
        return results
        
    except Exception as e:
        print(f"高性能分析Job月度数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return []
