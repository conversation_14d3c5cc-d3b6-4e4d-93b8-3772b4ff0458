# Pro2数据库基本操作

import asyncio
from typing import Dict, Any, List
from datetime import datetime, timedelta
from dotenv import load_dotenv
from utils.basic.db_pro2_sea_air_profit import (
    get_booking_details_with_transhipment,
    query_job_details_with_statistics_by_date
)
from utils.basic.logger_config import setup_logger
from utils.basic.data_cache_manager import async_cached_data_function

load_dotenv(override=True)

# 配置日志
logger = setup_logger(
    name=__name__,
    level="warning",
    log_to_console=True,
    log_to_file=True
)

# 批处理配置
BATCH_SIZE_DAYS = 3  # 每个批次处理的天数
MAX_CONCURRENT_BATCHES = 5  # 最大并发批次数

def _split_date_range(begin_date: str, end_date: str) -> List[tuple]:
    """
    将时间范围拆分为小批次
    """
    try:
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        
        batches = []
        current_date = start_date
        
        while current_date <= end_date_obj:
            batch_end = min(current_date + timedelta(days=BATCH_SIZE_DAYS - 1), end_date_obj)
            batches.append((
                current_date.strftime('%Y-%m-%d'),
                batch_end.strftime('%Y-%m-%d')
            ))
            current_date = batch_end + timedelta(days=1)
        
        return batches
    except Exception as e:
        logger.error(f"拆分日期范围失败: {e}")
        return [(begin_date, end_date)]

async def _process_batch_with_semaphore(semaphore, batch_func, batch_start, batch_end, logger_prefix):
    """
    使用信号量控制并发的批处理
    """
    async with semaphore:
        return await asyncio.to_thread(
            batch_func,
            batch_start,
            batch_end,
            logger_prefix=logger_prefix
        )


# 根据时间周期查询海运空运损益并添加转运利润字段 (调度器使用)
@async_cached_data_function
async def get_sea_air_profit_with_transhipment(begin_date: str, end_date: str) -> Dict[str, Any]:
    """
    根据时间周期查询海运空运损益并添加转运利润字段 (调度器使用)
    使用分批处理策略优化大时间范围查询性能
    """
    logger.warning(f"开始查询海空损益数据（含转运）: {begin_date} 到 {end_date}")
    
    try:
        # 计算时间范围
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        date_diff = (end_date_obj - start_date).days
        
        # 如果时间范围小于等于批次大小，直接查询
        if date_diff <= BATCH_SIZE_DAYS:
            logger.warning(f"时间范围较小({date_diff}天)，直接查询")
            results = await asyncio.to_thread(
                get_booking_details_with_transhipment,
                begin_date,
                end_date,
                logger_prefix="[Scheduler]"
            )
            
            logger.warning(f"海空损益数据查询完成（含转运）: {len(results)} 条记录")
            
            return {
                'data': results,
                'total_count': len(results),
                'query_info': {
                    'date_range': f'{begin_date} 到 {end_date}',
                    'data_type': '全部订舱毛利数据（含转运）'
                }
            }
        
        # 大时间范围使用分批处理
        logger.warning(f"时间范围较大({date_diff}天)，使用分批处理策略")
        batches = _split_date_range(begin_date, end_date)
        logger.warning(f"拆分为 {len(batches)} 个批次，每批次最多{BATCH_SIZE_DAYS}天")
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_BATCHES)
        
        # 创建批处理任务
        tasks = []
        for batch_start, batch_end in batches:
            task = _process_batch_with_semaphore(
                semaphore,
                get_booking_details_with_transhipment,
                batch_start,
                batch_end,
                f"[Scheduler-Batch-{batch_start}]"
            )
            tasks.append(task)
        
        # 并发执行所有批次
        logger.warning(f"开始并发执行 {len(tasks)} 个批次任务")
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 合并结果
        all_results = []
        failed_batches = []
        
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                failed_batches.append(batches[i])
                logger.error(f"批次 {batches[i]} 查询失败: {result}")
            else:
                all_results.extend(result)
                logger.info(f"批次 {batches[i]} 完成，获得 {len(result)} 条记录")
        
        logger.warning(f"所有批次处理完成，共获得 {len(all_results)} 条记录")
        if failed_batches:
            logger.warning(f"失败批次: {failed_batches}")
        
        return {
            'data': all_results,
            'total_count': len(all_results),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）',
                'batch_count': len(batches),
                'failed_batches': failed_batches
            }
        }
        
    except Exception as e:
        logger.error(f"查询海空损益数据失败（含转运）: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）',
                'error': f'查询失败: {str(e)}'
            }
        }

# 根据时间周期查询作业明细并添加转运利润字段 (调度器使用)
@async_cached_data_function
async def get_job_details_with_transhipment(begin_date: str, end_date: str) -> Dict[str, Any]:
    """
    根据时间周期查询作业明细并添加转运利润字段 (调度器使用)
    使用分批处理策略优化大时间范围查询性能
    """
    logger.warning(f"开始查询作业明细数据（含转运）: {begin_date} 到 {end_date}")
    
    try:
        # 计算时间范围
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        date_diff = (end_date_obj - start_date).days
        
        # 如果时间范围小于等于批次大小，直接查询
        if date_diff <= BATCH_SIZE_DAYS:
            logger.warning(f"时间范围较小({date_diff}天)，直接查询")
            results = await asyncio.to_thread(
                query_job_details_with_statistics_by_date,
                begin_date,
                end_date,
                logger_prefix="[Scheduler]"
            )
            
            if not results:
                logger.warning(f"未查询到作业明细数据: {begin_date} 到 {end_date}")
                return {
                    'data': [],
                    'total_count': 0,
                    'query_info': {
                        'date_range': f'{begin_date} 到 {end_date}',
                        'data_type': '全部作业明细数据（含转运）',
                        'message': '未查询到数据'
                    }
                }
            
            logger.warning(f"作业明细数据查询完成（含转运）: {len(results)} 条记录")
            
            return {
                'data': results,
                'total_count': len(results),
                'query_info': {
                    'date_range': f'{begin_date} 到 {end_date}',
                    'data_type': '全部作业明细数据（含转运）'
                }
            }
        
        # 大时间范围使用分批处理
        logger.warning(f"时间范围较大({date_diff}天)，使用分批处理策略")
        batches = _split_date_range(begin_date, end_date)
        logger.warning(f"拆分为 {len(batches)} 个批次，每批次最多{BATCH_SIZE_DAYS}天")
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_BATCHES)
        
        # 创建批处理任务
        tasks = []
        for batch_start, batch_end in batches:
            task = _process_batch_with_semaphore(
                semaphore,
                query_job_details_with_statistics_by_date,
                batch_start,
                batch_end,
                f"[Scheduler-Batch-{batch_start}]"
            )
            tasks.append(task)
        
        # 并发执行所有批次
        logger.warning(f"开始并发执行 {len(tasks)} 个批次任务")
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 合并结果
        all_results = []
        failed_batches = []
        
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                failed_batches.append(batches[i])
                logger.error(f"批次 {batches[i]} 查询失败: {result}")
            elif result:  # 确保结果不为空
                all_results.extend(result)
                logger.info(f"批次 {batches[i]} 完成，获得 {len(result)} 条记录")
        
        logger.warning(f"所有批次处理完成，共获得 {len(all_results)} 条记录")
        if failed_batches:
            logger.warning(f"失败批次: {failed_batches}")
        
        return {
            'data': all_results,
            'total_count': len(all_results),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）',
                'batch_count': len(batches),
                'failed_batches': failed_batches
            }
        }
        
    except Exception as e:
        logger.error(f"查询作业明细数据失败（含转运）: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）',
                'error': f'查询失败: {str(e)}'
            }
        }
