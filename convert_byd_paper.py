#!/usr/bin/env python3
"""
转换比亚迪财务分析论文为DOCX格式
"""

import asyncio
import os
from utils.basic.markdown_to_docx_converter import convert_markdown_to_docx

async def main():
    """转换比亚迪论文"""
    markdown_file = "/Users/<USER>/gitee-code-files/mcp-cms/logs/byd_financial_analysis_paper-v7.md"
    
    # 检查文件是否存在
    if not os.path.exists(markdown_file):
        print(f"❌ 文件不存在: {markdown_file}")
        return
    
    print("🔄 开始转换比亚迪财务分析论文...")
    print(f"📄 输入文件: {markdown_file}")
    
    try:
        # 转换文件
        docx_file = await convert_markdown_to_docx(markdown_file)
        
        print(f"✅ 转换成功!")
        print(f"📁 输出文件: {docx_file}")
        print(f"📊 文件大小: {os.path.getsize(docx_file) / 1024:.1f} KB")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())