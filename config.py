# 配置文件 - CMS Sin Profit 数据处理系统

import os
from datetime import datetime, time
from dotenv import load_dotenv

# 加载.env文件中的环境变量
load_dotenv()

class Config:
    """配置管理类"""
    
    # 文件路径配置
    DATA_DIR = os.getenv('DATA_DIR', 'data_files')
    LOG_DIR = os.getenv('LOG_DIR', 'logs')
    PROGRESS_FILE = os.getenv('PROGRESS_FILE', 'progress.json')
    
    # 时间间隔配置（秒）
    SLEEP_INTERVAL = int(os.getenv('SLEEP_INTERVAL', 3600))  # 1小时
    DATABASE_RETRY_INTERVAL = int(os.getenv('DATABASE_RETRY_INTERVAL', 2))  # 2秒
    
    # 数据库配置
    MAX_RETRY_ATTEMPTS = int(os.getenv('MAX_RETRY_ATTEMPTS', 3))
    BATCH_SIZE = int(os.getenv('BATCH_SIZE', 100))  # 批处理大小
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 文件验证配置
    MIN_FILE_SIZE = int(os.getenv('MIN_FILE_SIZE', 1024))  # 最小文件大小（字节）
    
    # 通知配置
    ENABLE_EMAIL_NOTIFICATION = os.getenv('ENABLE_EMAIL_NOTIFICATION', 'False').lower() == 'true'
    
    # 邮件服务器配置
    # 常见端口:
    # - 465: SSL加密端口 (Gmail, Outlook等)
    # - 587: STARTTLS端口 (大多数邮件服务商推荐)
    # - 25: 传统SMTP端口 (可能无加密，不推荐)
    EMAIL_SMTP_SERVER = os.getenv('EMAIL_SMTP_SERVER', '')
    EMAIL_SMTP_PORT = int(os.getenv('EMAIL_SMTP_PORT', 587))  # 默认587端口(STARTTLS)
    EMAIL_USERNAME = os.getenv('EMAIL_USERNAME', '')
    EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD', '')
    EMAIL_TO = [email.strip() for email in os.getenv('EMAIL_TO', '').split(',') if email.strip()]
    
    # Firebird 连接池配置
    FB_POOL_MAX_CONNECTIONS = int(os.getenv('FB_POOL_MAX_CONNECTIONS', 25))  # 增加到25个连接
    FB_POOL_MIN_CONNECTIONS = int(os.getenv('FB_POOL_MIN_CONNECTIONS', 3))   # 增加最小连接数
    FB_POOL_CONNECTION_REQUEST_TIMEOUT = int(os.getenv('FB_POOL_CONNECTION_REQUEST_TIMEOUT', 600)) # 增加到600秒
    FB_POOL_AUTO_SCALE = os.getenv('FB_POOL_AUTO_SCALE', 'True').lower() == 'true'

    # MySQL 连接配置
    MYSQL_CONNECT_TIMEOUT = int(os.getenv('MYSQL_CONNECT_TIMEOUT', 600)) # 600秒

    # 执行时间配置 - 每日20:00至次日8:00
    WEEKDAY_EXECUTION_TIMES = {
        0: "20:00-23:59, 00:00-08:00",  # 周一
        1: "20:00-23:59, 00:00-08:00",  # 周二
        2: "20:00-23:59, 00:00-08:00",  # 周三
        3: "20:00-23:59, 00:00-08:00",  # 周四
        4: "20:00-23:59, 00:00-08:00",  # 周五
        5: "20:00-23:59, 00:00-08:00",  # 周六
        6: "20:00-23:59, 00:00-08:00"   # 周日
    }

    # 默认执行时间范围（后备配置）
    DEFAULT_EXECUTION_TIME_RANGES_STR = "20:00-23:59"

    @classmethod
    def parse_execution_time_ranges(cls):
        """解析执行时间范围配置"""
        try:
            ranges = []
            # 注意：这个方法现在主要用于解析字符串格式的时间范围
            # get_execution_time_ranges_for_weekday 将会是主要的调用点
            time_ranges_str = cls.DEFAULT_EXECUTION_TIME_RANGES_STR.strip() # 使用默认后备
            
            if not time_ranges_str:
                # 如果没有配置，使用一个非常有限的默认时间范围或返回空
                return [] # 或者一个非常安全的默认值如 [(time(0,0), time(0,0))]
            
            for range_str in time_ranges_str.split(','):
                range_str = range_str.strip()
                if '-' not in range_str:
                    continue
                
                start_str, end_str = range_str.split('-', 1)
                start_str = start_str.strip()
                end_str = end_str.strip()
                
                # 解析开始时间
                if ':' in start_str:
                    start_hour, start_minute = map(int, start_str.split(':'))
                else:
                    start_hour, start_minute = int(start_str), 0
                
                # 解析结束时间
                if ':' in end_str:
                    end_hour, end_minute = map(int, end_str.split(':'))
                else:
                    end_hour, end_minute = int(end_str), 59
                
                start_time = time(start_hour, start_minute)
                end_time = time(end_hour, end_minute)
                ranges.append((start_time, end_time))
            
            return ranges if ranges else [] # 返回空列表如果解析后仍然为空
            
        except Exception as e:
            # 解析失败时使用一个非常有限的默认或空配置
            return []
    
    @classmethod
    def get_execution_time_ranges_for_weekday(cls, weekday):
        """根据星期几获取执行时间范围
        
        Args:
            weekday: 0=周一, 1=周二, ..., 6=周日
            
        Returns:
            List[Tuple[time, time]]: 时间范围列表
        """
        time_ranges_str = cls.WEEKDAY_EXECUTION_TIMES.get(weekday)
        
        parsed_ranges = []
        if time_ranges_str:
            try:
                for range_str in time_ranges_str.split(','):
                    range_str = range_str.strip()
                    if '-' not in range_str:
                        continue
                    
                    start_str, end_str = range_str.split('-', 1)
                    start_str = start_str.strip()
                    end_str = end_str.strip()
                    
                    if ':' in start_str:
                        start_hour, start_minute = map(int, start_str.split(':'))
                    else:
                        start_hour, start_minute = int(start_str), 0
                    
                    if ':' in end_str:
                        end_hour, end_minute = map(int, end_str.split(':'))
                    else:
                        end_hour, end_minute = int(end_str), 59
                    
                    start_time_obj = time(start_hour, start_minute)
                    end_time_obj = time(end_hour, end_minute)
                    parsed_ranges.append((start_time_obj, end_time_obj))
            except Exception:
                # 如果特定星期的字符串解析失败，可以考虑回退到默认或返回空
                pass # 保持 parsed_ranges 为空或当前已解析的部分

        # 如果特定星期没有配置或解析失败，可以决定是否回退到 DEFAULT_EXECUTION_TIME_RANGES_STR
        # 为了严格按您的需求，如果 WEEKDAY_EXECUTION_TIMES 中没有对应星期的条目，则不运行
        # 或者，如果希望有一个全局默认，可以在这里调用 cls.parse_execution_time_ranges(cls.DEFAULT_EXECUTION_TIME_RANGES_STR)
        # 当前逻辑：如果特定星期有配置则用它，否则该天无执行时间。
        if not parsed_ranges: # 如果特定星期解析后为空（例如配置错误或本身就没时间）
             # 你可以选择是否在这里使用一个全局默认值
             # 例如: return cls.parse_execution_time_ranges() # 这会使用 DEFAULT_EXECUTION_TIME_RANGES_STR
             pass # 当前：如果特定星期没成功解析出时间，则当天就没有执行时间

        return parsed_ranges
    
    @classmethod
    def is_execution_time(cls, current_datetime=None):
        """检查当前时间是否在执行时间范围内
        
        Args:
            current_datetime: 当前时间，如果为None则使用当前系统时间
            
        Returns:
            bool: 是否在执行时间内
        """
        if current_datetime is None:
            current_datetime = datetime.now()
        
        weekday = current_datetime.weekday()  # 0=周一, 6=周日
        current_time = current_datetime.time()
        
        time_ranges = cls.get_execution_time_ranges_for_weekday(weekday)
        
        for start_time, end_time in time_ranges:
            if start_time <= current_time <= end_time:
                return True
        
        return False
    
    @classmethod
    def get_config_dict(cls):
        """获取配置字典"""
        return {
            'execution_time_ranges': cls.parse_execution_time_ranges(),
            'data_dir': cls.DATA_DIR,
            'log_dir': cls.LOG_DIR,
            'progress_file': cls.PROGRESS_FILE,
            'sleep_interval': cls.SLEEP_INTERVAL,
            'database_retry_interval': cls.DATABASE_RETRY_INTERVAL,
            'max_retry_attempts': cls.MAX_RETRY_ATTEMPTS,
            'batch_size': cls.BATCH_SIZE,
            'min_file_size': cls.MIN_FILE_SIZE,
            'enable_email_notification': cls.ENABLE_EMAIL_NOTIFICATION,
            # 邮件配置
            'email_smtp_server': cls.EMAIL_SMTP_SERVER,
            'email_smtp_port': cls.EMAIL_SMTP_PORT,
            'email_username': cls.EMAIL_USERNAME,
            'email_password': cls.EMAIL_PASSWORD,
            'email_to': cls.EMAIL_TO,
            # Firebird 连接池配置
            'fb_pool_max_connections': cls.FB_POOL_MAX_CONNECTIONS,
            'fb_pool_min_connections': cls.FB_POOL_MIN_CONNECTIONS,
            'fb_pool_connection_request_timeout': cls.FB_POOL_CONNECTION_REQUEST_TIMEOUT,
            'fb_pool_auto_scale': cls.FB_POOL_AUTO_SCALE,
            # MySQL 连接配置
            'mysql_connect_timeout': cls.MYSQL_CONNECT_TIMEOUT
        }
    
    @classmethod
    def validate_config(cls):
        """验证配置参数"""
        errors = []
        
        # 验证时间范围（支持反向处理）
        if cls.REVERSE_PROCESSING:
            # 反向处理：从新到旧
            if cls.START_YEAR < cls.END_YEAR:
                errors.append("反向处理模式下，开始年份应大于等于结束年份")
            elif cls.START_YEAR == cls.END_YEAR and cls.START_MONTH < cls.END_MONTH:
                errors.append("反向处理模式下，同年内开始月份应大于等于结束月份")
        else:
            # 正向处理：从旧到新
            if cls.START_YEAR > cls.END_YEAR:
                errors.append("正向处理模式下，开始年份不能大于结束年份")
            elif cls.START_YEAR == cls.END_YEAR and cls.START_MONTH > cls.END_MONTH:
                errors.append("正向处理模式下，同年内开始月份不能大于结束月份")
        
        # 验证执行时间范围
        try:
            time_ranges = cls.parse_execution_time_ranges()
            if not time_ranges:
                errors.append("执行时间范围配置不能为空")
            else:
                for i, (start_time, end_time) in enumerate(time_ranges):
                    if start_time >= end_time:
                        errors.append(f"第{i+1}个时间范围的开始时间不能大于等于结束时间")
        except Exception as e:
            errors.append(f"执行时间范围配置格式错误: {e}")
        
        # 验证间隔时间
        if cls.SLEEP_INTERVAL < 60:
            errors.append("休眠间隔不能少于60秒")
        
        # 验证重试次数
        if cls.MAX_RETRY_ATTEMPTS < 1:
            errors.append("最大重试次数必须大于0")
        
        if errors:
            raise ValueError("配置验证失败: " + "; ".join(errors))
        
        return True 