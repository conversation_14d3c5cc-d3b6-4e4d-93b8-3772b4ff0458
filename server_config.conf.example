# 服务器配置文件示例
# 复制此文件为 server_config.conf 并修改相应配置

# 服务器地址配置
SERVER_QD="<EMAIL>"
SERVER_SHA="<EMAIL>"
SERVER_TYO="<EMAIL>"
SERVER_HKG="<EMAIL>"

# 远程路径配置
REMOTE_PATH_QD="/home/<USER>/mcp-cms/"
REMOTE_PATH_SHA="/home/<USER>/mcp-cms-sha/"
REMOTE_PATH_TYO="/root/mcp-cms-tyo/"
REMOTE_PATH_HKG="/root/mcp-cms-hkg/"

# 服务名称配置
SERVICE_QD="mcp-cms"
SERVICE_SHA="mcp-cms"
SERVICE_TYO="mcp-cms"
SERVICE_HKG="mcp-cms-hkg"

# 可选：自定义本地路径和密码
# LOCAL_PATH="/path/to/your/project"
# PASSWORD="your_password"