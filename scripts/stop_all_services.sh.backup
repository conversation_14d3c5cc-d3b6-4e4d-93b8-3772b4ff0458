#!/bin/bash

# MCP-CMS 停止所有服务器服务脚本
# 基于 transfer_fixed.sh 的服务停止功能

set -e

# 从配置文件加载服务器配置（如果存在）
load_config() {
    local config_file="${1:-server_config.conf}"
    if [ -f "$config_file" ]; then
        print_info "加载配置文件: $config_file"
        source "$config_file"
    fi
}

# 服务器配置函数
get_server() {
    case $1 in
        "qd") echo "${SERVER_QD:-<EMAIL>}";;
        "sha") echo "${SERVER_SHA:-<EMAIL>}";;
        "tyo") echo "${SERVER_TYO:-<EMAIL>}";;
        "hkg") echo "${SERVER_HKG:-<EMAIL>}";;
    esac
}

get_service_name() {
    case $1 in
        "qd") echo "${SERVICE_QD:-mcp-cms}";;
        "sha") echo "${SERVICE_SHA:-mcp-cms}";;
        "tyo") echo "${SERVICE_TYO:-mcp-cms}";;
        "hkg") echo "${SERVICE_HKG:-mcp-cms}";;
    esac
}

# 通用配置
PASSWORD="2929!lxj#LXJ"
HK_SSH_KEY="/Users/<USER>/Documents/pem-files/<EMAIL>"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 获取SSH连接命令
get_ssh_cmd() {
    local server_key="$1"
    if [[ "$server_key" == "hkg" ]]; then
        echo "ssh -i $HK_SSH_KEY -o StrictHostKeyChecking=no"
    else
        echo "sshpass -p \"$PASSWORD\" ssh -o StrictHostKeyChecking=no"
    fi
}

# 检查服务状态
check_service_status() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local service_name=$(get_service_name "$server_key")
    
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "echo '$PASSWORD' | sudo -S systemctl is-active $service_name" >/dev/null 2>&1; then
        return 0  # 服务运行中
    else
        return 1  # 服务未运行
    fi
}

# 跨平台超时命令
run_with_timeout() {
    local timeout_seconds="$1"
    shift
    
    if command -v gtimeout &> /dev/null; then
        gtimeout "$timeout_seconds" "$@"
    elif command -v timeout &> /dev/null; then
        timeout "$timeout_seconds" "$@"
    else
        # 使用 Bash 内置超时功能
        "$@" &
        local pid=$!
        (sleep "$timeout_seconds"; kill -9 "$pid" 2>/dev/null) &
        local killer_pid=$!
        wait "$pid" 2>/dev/null
        local exit_code=$?
        kill "$killer_pid" 2>/dev/null
        return $exit_code
    fi
}

# 强制终止相关进程
kill_mcp_processes() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    
    print_info "[$server_key] 查找并终止 MCP-CMS 相关进程..."
    
    # 第一轮：优雅终止 (SIGTERM)
    print_info "[$server_key] 第一轮: 优雅终止相关进程..."
    run_with_timeout 10 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
        "pkill -f 'mcp_server_cms.py' 2>/dev/null || true; pkill -f 'uv run mcp_server_cms.py' 2>/dev/null || true" || print_warning "[$server_key] 第一轮终止命令超时"
    
    # 等待进程响应 SIGTERM
    sleep 2
    
    # 第二轮：强制终止 (SIGKILL)
    print_info "[$server_key] 第二轮: 强制终止残留进程..."
    run_with_timeout 10 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
        "pkill -9 -f 'mcp_server_cms.py' 2>/dev/null || true; pkill -9 -f 'uv run mcp_server_cms.py' 2>/dev/null || true" || print_warning "[$server_key] 第二轮进程终止超时"
    
    # 终止所有 uvicorn 相关进程
    run_with_timeout 10 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
        "pkill -9 -f 'uvicorn.*mcp' 2>/dev/null || true" || print_warning "[$server_key] uvicorn进程终止超时"
    
    # 强制终止监听 MCP 端口的进程
    run_with_timeout 10 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
        "lsof -ti:8011,8012,8013,8014 2>/dev/null | xargs kill -9 2>/dev/null || true" || print_warning "[$server_key] 端口进程终止超时"
    
    # 额外的强制清理：使用 fuser 命令强制终止端口占用
    run_with_timeout 10 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
        "fuser -k 8011/tcp 8012/tcp 8013/tcp 8014/tcp 2>/dev/null || true" || print_warning "[$server_key] fuser清理超时"
    
    # 等待进程完全终止
    sleep 2
    
    # 第三轮验证和最终清理
    print_info "[$server_key] 第三轮: 验证和最终清理..."
    local cleanup_attempts=0
    local max_attempts=5
    
    while [[ $cleanup_attempts -lt $max_attempts ]]; do
        # 检查剩余进程
        local remaining_processes
        remaining_processes=$(run_with_timeout 15 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
            "ps aux | grep -E 'mcp_server_cms|uvicorn.*mcp|uv run mcp_server_cms' | grep -v grep | wc -l" 2>/dev/null || echo "0")
        
        # 检查端口占用
        local port_processes
        port_processes=$(run_with_timeout 15 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
            "lsof -ti:8011,8012,8013,8014 2>/dev/null | wc -l" 2>/dev/null || echo "0")
        
        if [[ "$remaining_processes" -eq 0 ]] && [[ "$port_processes" -eq 0 ]]; then
            print_success "[$server_key] 所有相关进程已终止"
            return 0
        fi
        
        print_warning "[$server_key] 第$((cleanup_attempts + 1))次清理: 仍有 $remaining_processes 个进程, $port_processes 个端口占用"
        
        # 更激进的清理
        if [[ "$remaining_processes" -gt 0 ]]; then
            run_with_timeout 15 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
                "ps aux | grep -E 'mcp_server_cms|uvicorn.*mcp|uv run mcp_server_cms' | grep -v grep | awk '{print \$2}' | xargs kill -9 2>/dev/null || true" || print_warning "[$server_key] 进程清理超时"
        fi
        
        if [[ "$port_processes" -gt 0 ]]; then
            run_with_timeout 15 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
                "lsof -ti:8011,8012,8013,8014 2>/dev/null | xargs kill -9 2>/dev/null || true" || print_warning "[$server_key] 端口清理超时"
        fi
        
        cleanup_attempts=$((cleanup_attempts + 1))
        sleep 1
    done
    
    # 最终状态报告
    local final_processes
    final_processes=$(run_with_timeout 15 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
        "ps aux | grep -E 'mcp_server_cms|uvicorn.*mcp|uv run mcp_server_cms' | grep -v grep | wc -l" 2>/dev/null || echo "0")
    
    if [[ "$final_processes" -eq 0 ]]; then
        print_success "[$server_key] 所有相关进程已终止"
    else
        print_error "[$server_key] 警告: 经过多次尝试仍有 $final_processes 个进程未能终止"
        # 显示具体的残留进程信息
        print_warning "[$server_key] 残留进程详情:"
        run_with_timeout 15 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
            "ps aux | grep -E 'mcp_server_cms|uvicorn.*mcp|uv run mcp_server_cms' | grep -v grep" 2>/dev/null || print_warning "[$server_key] 获取进程详情超时"
        
        # 最后的强制清理尝试
        print_warning "[$server_key] 执行最后的强制清理..."
        run_with_timeout 20 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
            "ps aux | grep -E 'mcp_server_cms|uvicorn.*mcp|uv run mcp_server_cms' | grep -v grep | awk '{print \$2}' | xargs -r kill -9 2>/dev/null || true" || print_warning "[$server_key] 最终清理超时"
        
        # 使用 killall 作为最后手段
        run_with_timeout 10 sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$server" \
            "killall -9 python3 2>/dev/null || true; killall -9 python 2>/dev/null || true" || print_warning "[$server_key] killall清理超时"
    fi
}

# 停止服务（增强版）
stop_service() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local service_name=$(get_service_name "$server_key")
    
    print_info "[$server_key] 检查服务 $service_name 状态..."
    
    # 先检查服务状态
    if check_service_status "$server_key"; then
        print_info "[$server_key] 服务正在运行，开始停止..."
        
        # 尝试正常停止服务
        if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" "echo '$PASSWORD' | sudo -S systemctl stop $service_name" 2>/dev/null; then
            sleep 3  # 等待服务停止
            
            # 再次检查服务状态
            if ! check_service_status "$server_key"; then
                print_success "[$server_key] 服务停止成功"
            else
                print_warning "[$server_key] 服务停止命令执行但服务仍在运行，尝试强制终止..."
                kill_mcp_processes "$server_key"
            fi
        else
            print_warning "[$server_key] systemctl stop 失败，尝试强制终止进程..."
            kill_mcp_processes "$server_key"
        fi
    else
        print_info "[$server_key] 服务未运行，检查是否有相关进程..."
        
        # 即使服务未运行，也检查是否有相关进程
        local process_count
        process_count=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
            "ps aux | grep -E 'mcp_server_cms|uvicorn.*mcp|uv run mcp_server_cms' | grep -v grep | wc -l" 2>/dev/null || echo "0")
        
        if [[ "$process_count" -gt 0 ]]; then
            print_warning "[$server_key] 发现 $process_count 个相关进程，正在终止..."
            kill_mcp_processes "$server_key"
        else
            print_success "[$server_key] 没有运行的服务或进程"
        fi
    fi
}

# 检查SSH连接
check_ssh_connection() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    
    print_info "[$server_key] 检查SSH连接到 $server..."
    if sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$server" "exit" 2>/dev/null; then
        print_success "[$server_key] SSH连接成功"
        return 0
    else
        print_error "[$server_key] SSH连接失败"
        return 1
    fi
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [配置文件] [服务器...]"
    echo ""
    echo "参数："
    echo "  配置文件     可选的配置文件路径"
    echo "  服务器       可选的服务器列表 (qd sha tyo hkg)，如不指定则停止所有服务器"
    echo ""
    echo "选项："
    echo "  --help, -h   显示此帮助信息"
    echo ""
    echo "示例："
    echo "  $0                    # 停止所有服务器的服务"
    echo "  $0 qd sha             # 只停止 QD 和 SHA 服务器的服务"
    echo "  $0 config.conf hkg    # 使用配置文件并停止 HKG 服务器的服务"
    echo ""
}

# 主函数
main() {
    local config_file=""
    local target_servers=()
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_usage
                exit 0
                ;;
            qd|sha|tyo|hkg)
                target_servers+=("$1")
                shift
                ;;
            *)
                if [[ -z "$config_file" ]]; then
                    if [[ -f "$1" ]]; then
                        config_file="$1"
                    elif [[ "$1" =~ ^[a-zA-Z0-9_.-]+$ ]]; then
                        config_file="$1"
                    fi
                fi
                shift
                ;;
        esac
    done
    
    load_config "$config_file"
    
    # 如果没有指定服务器，默认停止所有服务器
    if [ ${#target_servers[@]} -eq 0 ]; then
        target_servers=("qd" "sha" "tyo" "hkg")
    fi
    
    echo "🛑 开始停止 MCP-CMS 服务..."
    echo "🎯 目标服务器: ${target_servers[*]}"
    echo "⚙️  操作模式: 完整停止（包括服务和进程检查）"
    echo ""
    
    if ! command -v sshpass &> /dev/null; then
        print_error "sshpass未安装，请先安装: brew install sshpass"
        exit 1
    fi
    
    local failed_servers=()
    
    # 第一步：检查服务器连接
    print_info "第1步: 检查服务器连接..."
    for server_key in "${target_servers[@]}"; do
        if ! check_ssh_connection "$server_key"; then
            failed_servers+=("$server_key")
        fi
    done
    
    if [ ${#failed_servers[@]} -gt 0 ]; then
        print_error "以下服务器连接失败: ${failed_servers[*]}"
        print_warning "将跳过连接失败的服务器，继续停止其他服务器的服务"
        
        # 从目标服务器列表中移除连接失败的服务器
        local temp_servers=()
        for server_key in "${target_servers[@]}"; do
            if [[ ! " ${failed_servers[@]} " =~ " ${server_key} " ]]; then
                temp_servers+=("$server_key")
            fi
        done
        target_servers=("${temp_servers[@]}")
    fi
    
    if [ ${#target_servers[@]} -eq 0 ]; then
        print_error "没有可用的服务器，脚本退出"
        exit 1
    fi
    
    # 第二步：停止服务和进程
    print_info "第2步: 停止服务和相关进程..."
    for server_key in "${target_servers[@]}"; do
        stop_service "$server_key"
    done
    
    # 第三步：最终状态验证
    print_info "第3步: 验证停止状态..."
    local verification_failed=()
    for server_key in "${target_servers[@]}"; do
        print_info "[$server_key] 验证停止状态..."
        
        # 检查服务状态
        local service_running=false
        if check_service_status "$server_key"; then
            service_running=true
        fi
        
        # 检查进程状态
        local server=$(get_server "$server_key")
        local process_count
        process_count=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
            "ps aux | grep -E 'mcp_server_cms|uvicorn.*mcp|uv run mcp_server_cms' | grep -v grep | wc -l" 2>/dev/null || echo "0")
        
        # 检查端口占用
        local port_count
        port_count=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
            "lsof -ti:8011,8012,8013,8014 2>/dev/null | wc -l" 2>/dev/null || echo "0")
        
        if [[ "$service_running" == true ]] || [[ "$process_count" -gt 0 ]] || [[ "$port_count" -gt 0 ]]; then
            verification_failed+=("$server_key")
            print_warning "[$server_key] 停止验证失败 - 服务:$service_running, 进程:$process_count, 端口:$port_count"
        else
            print_success "[$server_key] 停止状态验证通过"
        fi
    done
    
    echo ""
    if [ ${#verification_failed[@]} -eq 0 ]; then
        print_success "所有服务停止操作完成并验证通过!"
    else
        print_warning "服务停止操作完成，但部分服务器验证失败"
    fi
    echo ""
    print_info "最终服务器状态:"
    for server_key in "${target_servers[@]}"; do
        if [[ " ${verification_failed[@]} " =~ " ${server_key} " ]]; then
            echo "⚠️  [$server_key] $(get_server "$server_key") - 停止不完整"
        else
            echo "✅ [$server_key] $(get_server "$server_key") - 完全停止"
        fi
    done
    
    if [ ${#failed_servers[@]} -gt 0 ]; then
        echo ""
        print_warning "跳过的服务器:"
        for server_key in "${failed_servers[@]}"; do
            echo "❌ [$server_key] $(get_server "$server_key") - 连接失败"
        done
    fi
    echo ""
}

trap 'print_error "脚本执行被中断"; exit 1' INT TERM

main "$@"