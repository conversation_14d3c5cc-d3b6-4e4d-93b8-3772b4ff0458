# Scripts 文件夹说明

本文件夹包含所有用于 MCP-CMS 部署和维护的脚本文件。

## 📜 脚本文件列表

### 主要部署脚本
- `reinit.sh` - 快速重新部署
- `transfer_fixed.sh` - 日常代码更新

### 服务管理脚本
- `start_all_services.sh` - 启动所有服务
- `stop_all_services.sh` - 停止所有服务
- `check_all_services.sh` - 检查服务状态
- `check_service_logs.sh` - 检查服务日志
- `check_sha_service.sh` - 检查上海服务器状态

## 🚀 快速使用

从项目根目录执行：

```bash
# 日常代码更新（最常用）
./scripts/transfer_fixed.sh

# 快速重新部署
./scripts/reinit.sh

# 检查所有服务状态
./scripts/check_all_services.sh
```

## ⚠️ 重要说明

- 这些脚本仅在本地使用，不会被同步到服务器
- 所有脚本的排除列表已更新，确保 `scripts/` 文件夹不会被传输到服务器
- 建议将常用脚本创建别名或软链接到根目录，方便使用

## 📋 别名建议

可以在项目根目录创建软链接：

```bash
# 创建常用脚本的软链接
ln -s scripts/transfer_fixed.sh update
ln -s scripts/reinit.sh redeploy  
ln -s scripts/check_all_services.sh status
```

然后就可以直接使用：
```bash
./update          # 更新代码
./redeploy        # 重新部署
./status          # 检查状态
``` 