#!/bin/bash

PASSWORD="2929!lxj#LXJ"

print_info() {
    echo -e "\033[0;34mℹ️  $1\033[0m"
}

print_error() {
    echo -e "\033[0;31m❌ $1\033[0m"
}

# 检查第一个服务器的日志
server="<EMAIL>"
print_info "检查 QD 服务器的服务日志..."

echo "=== 最近的服务日志 ==="
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
    "journalctl -u mcp-cms --no-pager -n 30"

echo ""
echo "=== 服务状态详情 ==="
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
    "systemctl status mcp-cms --no-pager -l"
