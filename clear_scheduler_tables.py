#!/usr/bin/env python3
"""
清空调度器相关的MySQL表数据脚本
清空表：t_booking_details, t_job_details, t_scheduler_check_log
默认清空所有记录（不区分系统ID）
"""

import asyncio
from utils.basic.data_conn_unified import get_mysql_connection, MYSQL_DB_MCP
from utils.basic.logger_config import setup_logger

# 配置日志
logger = setup_logger(__name__, level="info", log_to_console=True)

async def clear_scheduler_tables():
    """清空调度器相关的MySQL表数据（默认清空所有记录）"""
    
    # 要清空的表列表
    tables_to_clear = [
        't_booking_details',
        't_job_details', 
        't_scheduler_check_log'
    ]
    
    try:
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor() as cursor:
                
                # 先显示各表的记录数
                logger.info("=== 清空前的表记录统计 ===")
                for table in tables_to_clear:
                    await cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = await cursor.fetchone()
                    logger.info(f"{table}: {count[0]} 条记录")
                
                # 询问用户确认
                logger.info(f"\n⚠️  即将清空所有调度器数据表:")
                for table in tables_to_clear:
                    logger.info(f"  - {table}")
                
                confirm = input("\n确认要清空这些表的所有数据吗？(输入 'YES' 确认): ")
                
                if confirm != 'YES':
                    logger.info("操作已取消")
                    return False
                
                logger.info("\n开始清空表数据...")
                
                # 清空表数据（使用TRUNCATE提高速度）
                for table in tables_to_clear:
                    try:
                        sql = f"TRUNCATE TABLE {table}"
                        await cursor.execute(sql)
                        logger.info(f"✅ {table}: 表已清空（TRUNCATE）")
                        
                    except Exception as e:
                        logger.error(f"❌ 清空表 {table} 失败: {e}")
                
                # 提交事务
                await connection.commit()
                logger.info("\n所有操作已提交")
                
                # 显示清空后的记录数
                logger.info("\n=== 清空后的表记录统计 ===")
                for table in tables_to_clear:
                    await cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = await cursor.fetchone()
                    logger.info(f"{table}: {count[0]} 条记录")
                
                logger.info("\n🎉 表数据清空完成！")
                return True
                
    except Exception as e:
        logger.error(f"清空表数据失败: {e}")
        raise

async def reset_auto_increment():
    """重置表的自增ID（可选操作）"""
    
    tables_with_auto_increment = [
        't_booking_details',
        't_job_details',
        't_scheduler_check_log'
    ]
    
    try:
        async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
            async with connection.cursor() as cursor:
                
                logger.info("\n=== 重置自增ID ===")
                confirm = input("是否要重置表的自增ID？(输入 'YES' 确认): ")
                
                if confirm != 'YES':
                    logger.info("跳过自增ID重置")
                    return
                
                for table in tables_with_auto_increment:
                    try:
                        sql = f"ALTER TABLE {table} AUTO_INCREMENT = 1"
                        await cursor.execute(sql)
                        logger.info(f"✅ {table}: 自增ID已重置为1")
                    except Exception as e:
                        logger.error(f"❌ 重置 {table} 自增ID失败: {e}")
                
                await connection.commit()
                logger.info("自增ID重置完成")
                
    except Exception as e:
        logger.error(f"重置自增ID失败: {e}")

def main():
    """主函数"""
    logger.info("🚀 调度器表数据清空工具")
    logger.info("此工具将清空以下MySQL表的所有数据:")
    logger.info("  - t_booking_details (订舱详情)")
    logger.info("  - t_job_details (工作档详情)")
    logger.info("  - t_scheduler_check_log (调度器检查日志)")
    
    try:
        # 运行清空操作
        cleared = asyncio.run(clear_scheduler_tables())
        
        # 只有在成功清空表后才询问是否重置自增ID
        if cleared:
            asyncio.run(reset_auto_increment())
        
    except KeyboardInterrupt:
        logger.info("\n操作被用户中断")
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")

if __name__ == "__main__":
    main()